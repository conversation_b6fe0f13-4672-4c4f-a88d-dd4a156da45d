'use client'

import React, { useState, useEffect } from 'react'
import { 
  Package, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  Save,
  X
} from 'lucide-react'
import { SubscriptionService } from '@/lib/services/subscriptions'
import { SubscriptionPackage } from '@/types'
import { showConfirmation, showAlert } from '@/components/ui/ConfirmationDialog'
import { supabase } from '@/lib/supabase'

interface PackageFormData {
  name: string
  description: string
  price: string
  ad_limit: string
  boost_limit: string
  duration_days: string
  is_active: boolean
}

export default function SubscriptionPackagesPage() {
  const [packages, setPackages] = useState<SubscriptionPackage[]>([])
  const [loading, setLoading] = useState(true)
  const [editingPackage, setEditingPackage] = useState<SubscriptionPackage | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState<PackageFormData>({
    name: '',
    description: '',
    price: '',
    ad_limit: '',
    boost_limit: '',
    duration_days: '30',
    is_active: true
  })

  useEffect(() => {
    loadPackages()
  }, [])

  const loadPackages = async () => {
    try {
      setLoading(true)
      // Get all packages including inactive ones for admin
      const { data, error } = await supabase
        .from('subscription_packages')
        .select('*')
        .order('sort_order', { ascending: true })

      if (error) throw error
      setPackages(data || [])
    } catch (error) {
      console.error('Error loading packages:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to load subscription packages. Please ensure the database tables are created.',
        variant: 'danger'
      })
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      ad_limit: '',
      boost_limit: '',
      duration_days: '30',
      is_active: true
    })
    setEditingPackage(null)
    setShowForm(false)
  }

  const handleEdit = (pkg: SubscriptionPackage) => {
    setFormData({
      name: pkg.name,
      description: pkg.description || '',
      price: pkg.price.toString(),
      ad_limit: pkg.ad_limit.toString(),
      boost_limit: pkg.boost_limit.toString(),
      duration_days: pkg.duration_days.toString(),
      is_active: pkg.is_active
    })
    setEditingPackage(pkg)
    setShowForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim() || !formData.price || !formData.ad_limit || !formData.boost_limit) {
      await showAlert({
        title: 'Validation Error',
        message: 'Please fill in all required fields',
        variant: 'warning'
      })
      return
    }

    try {
      const packageData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: parseFloat(formData.price),
        ad_limit: parseInt(formData.ad_limit),
        boost_limit: parseInt(formData.boost_limit),
        duration_days: parseInt(formData.duration_days),
        is_active: formData.is_active
      }

      if (editingPackage) {
        // Update existing package
        const { error } = await supabase
          .from('subscription_packages')
          .update(packageData)
          .eq('id', editingPackage.id)

        if (error) throw error

        await showAlert({
          title: 'Success',
          message: 'Package updated successfully',
          variant: 'success'
        })
      } else {
        // Create new package
        const { error } = await supabase
          .from('subscription_packages')
          .insert(packageData)

        if (error) throw error

        await showAlert({
          title: 'Success',
          message: 'Package created successfully',
          variant: 'success'
        })
      }

      resetForm()
      await loadPackages()
    } catch (error) {
      console.error('Error saving package:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to save package',
        variant: 'danger'
      })
    }
  }

  const handleDelete = async (pkg: SubscriptionPackage) => {
    const confirmed = await showConfirmation({
      title: 'Delete Package',
      message: `Are you sure you want to delete "${pkg.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      variant: 'danger'
    })

    if (!confirmed) return

    try {
      const { error } = await supabase
        .from('subscription_packages')
        .delete()
        .eq('id', pkg.id)

      if (error) throw error

      await showAlert({
        title: 'Success',
        message: 'Package deleted successfully',
        variant: 'success'
      })

      await loadPackages()
    } catch (error) {
      console.error('Error deleting package:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to delete package',
        variant: 'danger'
      })
    }
  }

  const togglePackageStatus = async (pkg: SubscriptionPackage) => {
    try {
      const { error } = await supabase
        .from('subscription_packages')
        .update({ is_active: !pkg.is_active })
        .eq('id', pkg.id)

      if (error) throw error

      await loadPackages()
    } catch (error) {
      console.error('Error updating package status:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to update package status',
        variant: 'danger'
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
        </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Subscription Packages</h1>
            <p className="text-gray-600">Manage subscription packages and pricing</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Package
          </button>
        </div>

        {/* Form Modal */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">
                  {editingPackage ? 'Edit Package' : 'Add New Package'}
                </h2>
                <button
                  onClick={resetForm}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Package Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    placeholder="e.g., Starter Package"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    rows={2}
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    placeholder="Package description"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price (Rs) *
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Duration (Days) *
                    </label>
                    <input
                      type="number"
                      required
                      min="1"
                      value={formData.duration_days}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration_days: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ad Limit *
                    </label>
                    <input
                      type="number"
                      required
                      min="1"
                      value={formData.ad_limit}
                      onChange={(e) => setFormData(prev => ({ ...prev, ad_limit: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Boost Limit *
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      value={formData.boost_limit}
                      onChange={(e) => setFormData(prev => ({ ...prev, boost_limit: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded"
                  />
                  <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
                    Active (visible to users)
                  </label>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {editingPackage ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Packages List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {packages.map((pkg) => (
                <div
                  key={pkg.id}
                  className={`border rounded-lg p-4 ${
                    pkg.is_active ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-gray-900">{pkg.name}</h3>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => togglePackageStatus(pkg)}
                        className={`p-1 rounded ${
                          pkg.is_active ? 'text-green-600 hover:bg-green-100' : 'text-gray-400 hover:bg-gray-100'
                        }`}
                        title={pkg.is_active ? 'Active' : 'Inactive'}
                      >
                        {pkg.is_active ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                      </button>
                      <button
                        onClick={() => handleEdit(pkg)}
                        className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                        title="Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(pkg)}
                        className="p-1 text-red-600 hover:bg-red-100 rounded"
                        title="Delete"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Price:</span>
                      <span className="font-medium">Rs {pkg.price.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Ads:</span>
                      <span className="font-medium">{pkg.ad_limit}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Boosts:</span>
                      <span className="font-medium">{pkg.boost_limit}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span className="font-medium">{pkg.duration_days} days</span>
                    </div>
                  </div>

                  {pkg.description && (
                    <p className="text-xs text-gray-500 mt-3">{pkg.description}</p>
                  )}
                </div>
              ))}
            </div>

            {packages.length === 0 && (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No packages found</h3>
                <p className="text-gray-600 mb-4">Create your first subscription package to get started.</p>
                <button
                  onClick={() => setShowForm(true)}
                  className="inline-flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Package
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
  )
}
