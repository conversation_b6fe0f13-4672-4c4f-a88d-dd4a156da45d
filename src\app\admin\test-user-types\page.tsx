'use client'

import React, { useState } from 'react'
import { Crown, Shield, Network, User } from 'lucide-react'

export default function TestUserTypesPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any[]>([])

  const updateTestUserTypes = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/update-test-user-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      
      if (data.success) {
        setResults(data.results)
        alert('User types updated successfully!')
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error updating user types:', error)
      alert('Failed to update user types')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Test User Types</h1>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Update Test User Types</h2>
              <p className="text-gray-600 mb-4">
                This will update some test users to have different user_type values for testing the referral rank badges.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="flex items-center space-x-2 p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <Crown className="h-5 w-5 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">OKDOI Head</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <Shield className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium text-purple-800">Zonal Manager</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-green-50 rounded-lg border border-green-200">
                  <Network className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">RSM</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <User className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">User</span>
                </div>
              </div>

              <button
                onClick={updateTestUserTypes}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Updating...' : 'Update Test User Types'}
              </button>
            </div>

            {results.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Update Results</h3>
                <div className="space-y-3">
                  {results.map((result, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        result.status === 'updated'
                          ? 'bg-green-50 border-green-200'
                          : result.status === 'error'
                          ? 'bg-red-50 border-red-200'
                          : 'bg-yellow-50 border-yellow-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{result.email}</p>
                          <p className="text-sm text-gray-600">{result.message}</p>
                          {result.old_type && result.new_type && (
                            <p className="text-xs text-gray-500">
                              Changed from {result.old_type} to {result.new_type}
                            </p>
                          )}
                        </div>
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded-full ${
                            result.status === 'updated'
                              ? 'bg-green-100 text-green-800'
                              : result.status === 'error'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {result.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
  )
}
