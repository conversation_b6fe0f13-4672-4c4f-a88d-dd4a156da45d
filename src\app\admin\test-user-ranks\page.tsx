'use client'

import React, { useState } from 'react'
import { Crown, Shield, Network, User, RefreshCw } from 'lucide-react'
import ReferralRankBadge from '@/components/ui/ReferralRankBadge'

interface UserRankData {
  id: string
  email: string
  full_name?: string
  user_type: string
}

export default function TestUserRanksPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [currentRanks, setCurrentRanks] = useState<any>(null)

  const createTestRanks = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/test-user-ranks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'create_test_ranks' })
      })

      const data = await response.json()
      setResults(data)
      
      if (data.success) {
        alert('Test user ranks created successfully!')
        getCurrentRanks() // Refresh current ranks
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error creating test ranks:', error)
      alert('Failed to create test ranks')
    } finally {
      setLoading(false)
    }
  }

  const resetRanks = async () => {
    if (!confirm('Are you sure you want to reset all user ranks to default?')) {
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/admin/test-user-ranks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'reset_ranks' })
      })

      const data = await response.json()
      setResults(data)
      
      if (data.success) {
        alert('All user ranks reset successfully!')
        getCurrentRanks() // Refresh current ranks
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error resetting ranks:', error)
      alert('Failed to reset ranks')
    } finally {
      setLoading(false)
    }
  }

  const getCurrentRanks = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/test-user-ranks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'get_current_ranks' })
      })

      const data = await response.json()
      setCurrentRanks(data)
      
      if (!data.success) {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error getting current ranks:', error)
      alert('Failed to get current ranks')
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return <Crown className="h-5 w-5 text-amber-500" />
      case 'zonal_manager':
        return <Shield className="h-5 w-5 text-purple-500" />
      case 'rsm':
        return <Network className="h-5 w-5 text-green-500" />
      default:
        return <User className="h-5 w-5 text-gray-500" />
    }
  }

  const getRankName = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return 'OKDOI Head'
      case 'zonal_manager':
        return 'Zonal Manager'
      case 'rsm':
        return 'Regional Sales Manager'
      default:
        return 'User'
    }
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Test User Ranks</h1>
            <p className="text-gray-600">Create test user ranks to verify badge display functionality</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold mb-4">Actions</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={createTestRanks}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Crown className="h-4 w-4 mr-2" />}
              Create Test Ranks
            </button>
            
            <button
              onClick={resetRanks}
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center"
            >
              {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
              Reset All Ranks
            </button>
            
            <button
              onClick={getCurrentRanks}
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center"
            >
              {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
              Get Current Ranks
            </button>
          </div>
        </div>

        {/* Current Ranks Display */}
        {currentRanks && currentRanks.success && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">Current User Ranks</h2>
            
            {/* Sample Users */}
            <div className="mb-6">
              <h3 className="text-md font-medium mb-3">Sample Users with Ranks</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {currentRanks.sample_users?.map((user: UserRankData) => (
                  <div key={user.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {getRankIcon(user.user_type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {user.full_name || 'No name'}
                        </p>
                        <p className="text-sm text-gray-500 truncate">{user.email}</p>
                        <div className="mt-2">
                          <ReferralRankBadge
                            userType={user.user_type as any}
                            showLabel={true}
                            size="sm"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Results Display */}
        {results && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">Last Action Results</h2>
            <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-auto">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        )}
      </div>
  )
}
