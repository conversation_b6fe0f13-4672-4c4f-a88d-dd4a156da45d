'use client'

import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Zap, Clock, DollarSign } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { showAlert, showConfirmation } from '@/components/ui/ConfirmationDialog'

interface BoostPackage {
  id: string
  name: string
  description: string
  duration_days: number
  price: number
  currency: string
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export default function BoostPackagesPage() {
  const [packages, setPackages] = useState<BoostPackage[]>([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingPackage, setEditingPackage] = useState<BoostPackage | null>(null)

  useEffect(() => {
    fetchPackages()
  }, [])

  const fetchPackages = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('boost_packages')
        .select('*')
        .order('sort_order')

      if (error) throw error
      setPackages(data || [])
    } catch (error) {
      console.error('Error fetching packages:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to fetch boost packages',
        variant: 'danger'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (pkg: BoostPackage) => {
    setEditingPackage(pkg)
    setShowModal(true)
  }

  const handleDelete = async (pkg: BoostPackage) => {
    const confirmed = await showConfirmation({
      title: 'Delete Boost Package',
      message: `Are you sure you want to delete "${pkg.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      variant: 'danger'
    })

    if (!confirmed) return

    try {
      const { error } = await supabase
        .from('boost_packages')
        .delete()
        .eq('id', pkg.id)

      if (error) throw error

      await showAlert({
        title: 'Success',
        message: 'Boost package deleted successfully',
        variant: 'success'
      })

      fetchPackages()
    } catch (error) {
      console.error('Error deleting package:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to delete boost package',
        variant: 'danger'
      })
    }
  }

  const handleToggleActive = async (pkg: BoostPackage) => {
    try {
      const { error } = await supabase
        .from('boost_packages')
        .update({ 
          is_active: !pkg.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', pkg.id)

      if (error) throw error

      await showAlert({
        title: 'Success',
        message: `Package ${!pkg.is_active ? 'activated' : 'deactivated'} successfully`,
        variant: 'success'
      })

      fetchPackages()
    } catch (error) {
      console.error('Error updating package:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to update package status',
        variant: 'danger'
      })
    }
  }

  if (loading) {
    return (
      <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-20 bg-gray-300 rounded"></div>
            ))}
          </div>
        </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Boost Packages</h1>
            <p className="text-gray-600">Manage boost packages and pricing</p>
          </div>
          <button
            onClick={() => {
              setEditingPackage(null)
              setShowModal(true)
            }}
            className="bg-primary-blue text-white px-4 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Package
          </button>
        </div>

        {/* Packages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {packages.map((pkg) => (
            <div
              key={pkg.id}
              className={`bg-white rounded-xl shadow-sm border p-6 ${
                pkg.is_active ? 'border-gray-200' : 'border-gray-300 opacity-60'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <Zap className="h-6 w-6 text-orange-500 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">{pkg.name}</h3>
                </div>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    pkg.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {pkg.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              <p className="text-gray-600 text-sm mb-4">{pkg.description}</p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-2" />
                  {pkg.duration_days} days
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Rs {pkg.price.toLocaleString()}
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => handleEdit(pkg)}
                  className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center text-sm"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </button>
                <button
                  onClick={() => handleToggleActive(pkg)}
                  className={`flex-1 px-3 py-2 rounded-lg transition-colors flex items-center justify-center text-sm ${
                    pkg.is_active
                      ? 'bg-yellow-50 text-yellow-600 hover:bg-yellow-100'
                      : 'bg-green-50 text-green-600 hover:bg-green-100'
                  }`}
                >
                  {pkg.is_active ? 'Deactivate' : 'Activate'}
                </button>
                <button
                  onClick={() => handleDelete(pkg)}
                  className="bg-red-50 text-red-600 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors flex items-center justify-center"
                >
                  <Trash2 className="h-3 w-3" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {packages.length === 0 && (
          <div className="text-center py-12">
            <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No boost packages</h3>
            <p className="text-gray-600 mb-4">Get started by creating your first boost package.</p>
            <button
              onClick={() => {
                setEditingPackage(null)
                setShowModal(true)
              }}
              className="bg-primary-blue text-white px-4 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors"
            >
              Add Package
            </button>
          </div>
        )}
      </div>

      {/* Package Modal */}
      {showModal && (
        <PackageModal
          package={editingPackage}
          onClose={() => {
            setShowModal(false)
            setEditingPackage(null)
          }}
          onSuccess={() => {
            setShowModal(false)
            setEditingPackage(null)
            fetchPackages()
          }}
        />
      )}
  )
}

// Package Modal Component
function PackageModal({
  package: pkg,
  onClose,
  onSuccess
}: {
  package: BoostPackage | null
  onClose: () => void
  onSuccess: () => void
}) {
  const [formData, setFormData] = useState({
    name: pkg?.name || '',
    description: pkg?.description || '',
    duration_days: pkg?.duration_days || 3,
    price: pkg?.price || 0,
    currency: pkg?.currency || 'LKR',
    is_active: pkg?.is_active ?? true,
    sort_order: pkg?.sort_order || 0
  })
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim() || formData.price <= 0 || formData.duration_days <= 0) {
      await showAlert({
        title: 'Validation Error',
        message: 'Please fill in all required fields with valid values',
        variant: 'warning'
      })
      return
    }

    setLoading(true)

    try {
      if (pkg) {
        // Update existing package
        const { error } = await supabase
          .from('boost_packages')
          .update({
            ...formData,
            updated_at: new Date().toISOString()
          })
          .eq('id', pkg.id)

        if (error) throw error
      } else {
        // Create new package
        const { error } = await supabase
          .from('boost_packages')
          .insert([formData])

        if (error) throw error
      }

      await showAlert({
        title: 'Success',
        message: `Boost package ${pkg ? 'updated' : 'created'} successfully`,
        variant: 'success'
      })

      onSuccess()
    } catch (error) {
      console.error('Error saving package:', error)
      await showAlert({
        title: 'Error',
        message: `Failed to ${pkg ? 'update' : 'create'} boost package`,
        variant: 'danger'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            {pkg ? 'Edit' : 'Add'} Boost Package
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Plus className="h-5 w-5 text-gray-500 rotate-45" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Package Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              placeholder="e.g., 3-Day Boost"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              rows={3}
              placeholder="Package description..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Duration (Days) *
              </label>
              <input
                type="number"
                value={formData.duration_days}
                onChange={(e) => setFormData({ ...formData, duration_days: parseInt(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                min="1"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price (Rs) *
              </label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                min="0"
                step="0.01"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort Order
            </label>
            <input
              type="number"
              value={formData.sort_order}
              onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              min="0"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700">
              Active (visible to users)
            </label>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : pkg ? 'Update' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
