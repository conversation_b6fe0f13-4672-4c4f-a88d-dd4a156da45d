'use client'

import React, { useState } from 'react'
import { Database, TrendingUp, Users, DollarSign } from 'lucide-react'

export default function TestCommissionDataPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const createSampleData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/create-sample-commission-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      
      if (data.success) {
        setResult(data)
        alert('Sample commission data created successfully!')
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error creating sample data:', error)
      alert('Failed to create sample commission data')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Test Commission Data</h1>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Create Sample Commission Data</h2>
              <p className="text-gray-600 mb-4">
                This will create sample commission transaction data for testing the commission breakdown functionality.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">Direct Commission</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-green-50 rounded-lg border border-green-200">
                  <Users className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">Level Commission</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <DollarSign className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium text-purple-800">Bonuses & Allowances</span>
                </div>
                <div className="flex items-center space-x-2 p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <Database className="h-5 w-5 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">All Categories</span>
                </div>
              </div>

              <button
                onClick={createSampleData}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating Sample Data...' : 'Create Sample Commission Data'}
              </button>
            </div>

            {result && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Creation Results</h3>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <Database className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-green-900">{result.message}</p>
                      <div className="text-sm text-green-700 mt-1">
                        <p>Commissions Created: {result.data?.commissionsCreated}</p>
                        <p>Users Processed: {result.data?.usersProcessed}</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Next Steps:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Visit the wallet page to see the commission breakdown</li>
                    <li>• Check different user types (ZM, RSM) for role-specific commissions</li>
                    <li>• Test the commission structure management page</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
  )
}
