import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testCommissionSystem() {
  console.log('Testing Commission System...')
  
  try {
    // Check commission transactions status
    const { data: commissions, error } = await supabase
      .from('commission_transactions')
      .select('status, count(*)')
      .group('status')
    
    if (error) {
      console.error('Error fetching commission transactions:', error)
      return
    }
    
    console.log('Commission Transactions by Status:')
    commissions?.forEach(item => {
      console.log(`- ${item.status}: ${item.count}`)
    })
    
    // Check recent commission transactions
    const { data: recentCommissions, error: recentError } = await supabase
      .from('commission_transactions')
      .select(`
        id, status, commission_amount, commission_type, commission_level,
        created_at, beneficiary_id, wallet_transaction_id
      `)
      .order('created_at', { ascending: false })
      .limit(10)
    
    if (recentError) {
      console.error('Error fetching recent commissions:', recentError)
      return
    }
    
    console.log('\nRecent Commission Transactions:')
    recentCommissions?.forEach(commission => {
      console.log(`- ID: ${commission.id}`)
      console.log(`  Status: ${commission.status}`)
      console.log(`  Amount: ${commission.commission_amount}`)
      console.log(`  Type: ${commission.commission_type}`)
      console.log(`  Level: ${commission.commission_level}`)
      console.log(`  Wallet Transaction ID: ${commission.wallet_transaction_id || 'None'}`)
      console.log(`  Created: ${commission.created_at}`)
      console.log('---')
    })
    
    // Check if calculate_commission_distribution function exists
    const { data: functions, error: funcError } = await supabase
      .rpc('exec', { 
        sql: `SELECT routine_name FROM information_schema.routines 
              WHERE routine_schema = 'public' 
              AND routine_name = 'calculate_commission_distribution';` 
      })
    
    if (funcError) {
      console.error('Error checking function:', funcError)
    } else {
      console.log('\nDatabase Function Status:')
      console.log('calculate_commission_distribution exists:', functions?.length > 0 ? 'Yes' : 'No')
    }
    
  } catch (error) {
    console.error('Test failed:', error)
  }
}

testCommissionSystem()
