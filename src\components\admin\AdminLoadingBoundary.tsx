'use client'

import { Suspense } from 'react'
import { Loader2 } from 'lucide-react'

interface AdminLoadingBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

function DefaultLoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-amber-500" />
        <p className="text-sm text-gray-600">Loading...</p>
      </div>
    </div>
  )
}

export default function AdminLoadingBoundary({ 
  children, 
  fallback = <DefaultLoadingFallback /> 
}: AdminLoadingBoundaryProps) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  )
}
