'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  BarChart3,
  Shield,
  Menu,
  X,
  LogOut,
  ArrowLeft,
  Banknote,
  Download,
  Crown,
  Zap,
  FolderTree,
  Store,
  MapPin,
  Network,
  TrendingUp,
  ChevronDown,
  ChevronRight,
  Layers
} from 'lucide-react'
import { AdminLogo } from '@/components/ui/Logo'
import { AdminService } from '@/lib/services/admin'
import { supabase } from '@/lib/supabase'
import NavigationProgress from './NavigationProgress'

interface AdminLayoutProps {
  children: React.ReactNode
}

interface NavigationItem {
  name: string
  href?: string
  icon: any
  children?: NavigationItem[]
  isCategory?: boolean
}

const navigation: NavigationItem[] = [
  { name: 'User Dashboard', href: '/dashboard', icon: ArrowLeft },
  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
  { name: 'Users', href: '/admin/users', icon: Users },
  {
    name: 'Classified Ads',
    icon: Layers,
    isCategory: true,
    children: [
      { name: 'Ads', href: '/admin/ads', icon: FileText },
      { name: 'Categories', href: '/admin/categories', icon: FolderTree },
      { name: 'Locations', href: '/admin/locations', icon: MapPin },
      { name: 'Subscriptions', href: '/admin/subscriptions/packages', icon: Crown },
      { name: 'Boost Packages', href: '/admin/boost-packages', icon: Zap },
    ]
  },
  {
    name: 'Online Shops',
    icon: Store,
    isCategory: true,
    children: [
      { name: 'Shop Categories', href: '/admin/shop-categories', icon: FolderTree },
      { name: 'Shops', href: '/admin/shops', icon: Shield },
    ]
  },
  {
    name: 'Finance',
    icon: Banknote,
    isCategory: true,
    children: [
      { name: 'Deposits', href: '/admin/deposits', icon: Banknote },
      { name: 'Withdrawals', href: '/admin/withdrawals', icon: Download },
    ]
  },
  {
    name: 'Network',
    icon: Network,
    isCategory: true,
    children: [
      { name: 'Referral System', href: '/admin/referrals', icon: Network },
      { name: 'Commission Structure', href: '/admin/commission-structure', icon: TrendingUp },
      { name: 'Commission Reports', href: '/admin/commission-reports', icon: BarChart3 },
    ]
  },
  { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },
  { name: 'Settings', href: '/admin/settings', icon: Settings },
]

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)
  const [loading, setLoading] = useState(true)
  const [expandedCategories, setExpandedCategories] = useState<string[]>([])
  const [manuallyCollapsed, setManuallyCollapsed] = useState<Set<string>>(new Set())
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    checkAdminAccess()
  }, [])

  // Auto-expand categories when their children are active, but only if not manually collapsed
  useEffect(() => {
    const activeCategory = navigation.find(item =>
      item.isCategory && item.children &&
      item.children.some(child => child.href === pathname)
    )

    if (activeCategory && !manuallyCollapsed.has(activeCategory.name)) {
      setExpandedCategories(prev => {
        if (!prev.includes(activeCategory.name)) {
          const newExpanded = [...prev, activeCategory.name]
          // Keep only the last 2 expanded categories
          return newExpanded.slice(-2)
        }
        return prev
      })
    }
  }, [pathname, manuallyCollapsed])

  const checkAdminAccess = async () => {
    try {
      console.log('AdminLayout: Checking admin access...')
      await AdminService.debugCurrentUser()
      const adminStatus = await AdminService.isAdmin()
      console.log('AdminLayout: Admin status:', adminStatus)
      setIsAdmin(adminStatus)

      if (!adminStatus) {
        // Check if user is authenticated but not admin
        const { data: { user } } = await supabase.auth.getUser()
        console.log('AdminLayout: Current user:', user?.email)
        if (user) {
          // User is authenticated but not admin, show access denied for 3 seconds then redirect
          console.log('AdminLayout: User authenticated but not admin, showing access denied')
          setTimeout(() => {
            console.log('AdminLayout: Redirecting to dashboard after delay')
            router.push('/dashboard')
          }, 3000)
        } else {
          // User is not authenticated, redirect to signin with current path
          console.log('AdminLayout: User not authenticated, redirecting to signin')
          const currentPath = window.location.pathname
          const redirectUrl = `/auth/signin?redirect=${encodeURIComponent(currentPath)}`
          router.push(redirectUrl)
        }
      } else {
        console.log('AdminLayout: User is admin, allowing access')
      }
    } catch (error) {
      console.error('Error checking admin access:', error)
      // On error, redirect to signin with current path
      const currentPath = window.location.pathname
      const redirectUrl = `/auth/signin?redirect=${encodeURIComponent(currentPath)}`
      router.push(redirectUrl)
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/')
  }

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev => {
      const isCurrentlyExpanded = prev.includes(categoryName)

      if (isCurrentlyExpanded) {
        // Remove from expanded categories
        const newExpanded = prev.filter(name => name !== categoryName)
        // Mark as manually collapsed
        setManuallyCollapsed(prevCollapsed => new Set([...prevCollapsed, categoryName]))
        return newExpanded
      } else {
        // Add to expanded categories
        let newExpanded = [...prev, categoryName]

        // Keep only the last 2 expanded categories (remove the first if we have more than 2)
        if (newExpanded.length > 2) {
          newExpanded = newExpanded.slice(-2)
        }

        // Remove from manually collapsed when manually expanded
        setManuallyCollapsed(prevCollapsed => {
          const newCollapsed = new Set(prevCollapsed)
          newCollapsed.delete(categoryName)
          return newCollapsed
        })

        return newExpanded
      }
    })
  }

  const handleNavigation = (isMobile: boolean = false) => {
    if (isMobile) {
      setSidebarOpen(false)
    }
  }

  const isChildActive = (children: NavigationItem[]) => {
    return children.some(child => child.href === pathname)
  }

  const renderNavigationItem = (item: NavigationItem, isMobile: boolean = false) => {
    if (item.isCategory && item.children) {
      const isExpanded = expandedCategories.includes(item.name)
      const hasActiveChild = isChildActive(item.children)

      return (
        <div key={item.name} className="space-y-1">
          <button
            onClick={() => toggleCategory(item.name)}
            className={`flex items-center justify-between w-full px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 whitespace-nowrap ${
              hasActiveChild
                ? 'bg-gray-900 text-white shadow-md'
                : 'text-gray-900 hover:bg-amber-200 hover:shadow-sm'
            }`}
          >
            <div className="flex items-center">
              <item.icon className="h-5 w-5 mr-3" />
              {item.name}
            </div>
            <div className={`transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}>
              <ChevronDown className="h-4 w-4" />
            </div>
          </button>

          <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
            isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}>
            <div className="ml-6 space-y-1 border-l-2 border-amber-300 pl-6">
              {item.children.map((child) => {
                const isActive = pathname === child.href
                return (
                  <Link
                    key={child.name}
                    href={child.href!}
                    prefetch={true}
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 whitespace-nowrap ${
                      isActive
                        ? 'bg-gray-800 text-white shadow-md transform scale-105'
                        : 'text-gray-700 hover:bg-amber-100 hover:text-gray-900 hover:shadow-sm'
                    }`}
                    onClick={() => handleNavigation(isMobile)}
                  >
                    <child.icon className="h-4 w-4 mr-3" />
                    {child.name}
                  </Link>
                )
              })}
            </div>
          </div>
        </div>
      )
    }

    // Regular navigation item
    const isActive = pathname === item.href
    return (
      <Link
        key={item.name}
        href={item.href!}
        prefetch={true}
        className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 whitespace-nowrap ${
          isActive
            ? 'bg-gray-900 text-white shadow-md'
            : 'text-gray-900 hover:bg-amber-200 hover:shadow-sm'
        }`}
        onClick={() => handleNavigation(isMobile)}
      >
        <item.icon className="h-5 w-5 mr-3" />
        {item.name}
      </Link>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-6">You don't have permission to access this area.</p>
          <div className="space-y-3">
            <button
              onClick={checkAdminAccess}
              className="w-full px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
            >
              Retry Access Check
            </button>
            <button
              onClick={() => router.push('/dashboard')}
              className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-4">
            If you believe this is an error, please check the browser console for more details.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <NavigationProgress />
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-80 flex-col shadow-xl" style={{ backgroundColor: '#FFBF00' }}>
          <div className="flex h-16 items-center justify-between px-6 border-b border-amber-300">
            <AdminLogo />
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-6 py-4 space-y-2">
            {navigation.map((item) => renderNavigationItem(item, true))}
          </nav>
          <div className="p-6 border-t border-amber-300">
            <button
              onClick={handleSignOut}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-amber-200 rounded-lg transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-80 lg:flex-col">
        <div className="flex flex-col flex-grow border-r border-amber-300 shadow-sm" style={{ backgroundColor: '#FFBF00' }}>
          <div className="flex h-16 items-center px-6 border-b border-amber-300">
            <AdminLogo />
          </div>
          <nav className="flex-1 px-6 py-4 space-y-2">
            {navigation.map((item) => renderNavigationItem(item, false))}
          </nav>
          <div className="p-6 border-t border-amber-300">
            <button
              onClick={handleSignOut}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 hover:bg-amber-200 rounded-lg transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-80">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                {(() => {
                  // Find the current page name from navigation
                  for (const item of navigation) {
                    if (item.href === pathname) {
                      return item.name
                    }
                    if (item.children) {
                      const child = item.children.find(child => child.href === pathname)
                      if (child) {
                        return child.name
                      }
                    }
                  }
                  return 'Admin Panel'
                })()}
              </h2>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-8">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
