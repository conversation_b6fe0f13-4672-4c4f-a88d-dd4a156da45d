'use client'

import React, { useState, useEffect } from 'react'
import { BarChart3, TrendingUp, Users, DollarSign, Download, Calendar, Filter, Eye } from 'lucide-react'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { CommissionSystemService } from '@/lib/services/commissionSystem'
import { ReferralSystemService } from '@/lib/services/referralSystem'
import { CommissionTransaction } from '@/types'

interface CommissionReportData {
  totalCommissionsPaid: number
  pendingCommissions: number
  failedCommissions: number
  totalTransactions: number
  topEarners: { userId: string, fullName: string, totalEarned: number }[]
  monthlyData: { month: string, amount: number, count: number }[]
  levelDistribution: { level: number, amount: number, count: number }[]
  typeDistribution: { type: string, amount: number, count: number }[]
}

export default function CommissionReportsPage() {
  const [reportData, setReportData] = useState<CommissionReportData>({
    totalCommissionsPaid: 0,
    pendingCommissions: 0,
    failedCommissions: 0,
    totalTransactions: 0,
    topEarners: [],
    monthlyData: [],
    levelDistribution: [],
    typeDistribution: []
  })
  const [transactions, setTransactions] = useState<CommissionTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    status: '',
    commissionType: '',
    level: ''
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    loadReportData()
  }, [])

  useEffect(() => {
    loadTransactions()
  }, [filters, currentPage])

  const loadReportData = async () => {
    try {
      setLoading(true)

      const [commissionSummary, analytics] = await Promise.all([
        CommissionSystemService.getCommissionSummary(),
        CommissionSystemService.getCommissionAnalytics()
      ])

      setReportData({
        totalCommissionsPaid: commissionSummary.totalCommissionsPaid,
        pendingCommissions: commissionSummary.pendingCommissions,
        failedCommissions: commissionSummary.failedCommissions,
        totalTransactions: commissionSummary.totalTransactions,
        topEarners: commissionSummary.topEarners,
        monthlyData: analytics.monthlyData,
        levelDistribution: analytics.levelDistribution,
        typeDistribution: analytics.typeDistribution
      })
    } catch (error) {
      console.error('Error loading report data:', error)
      setError('Failed to load report data')
    } finally {
      setLoading(false)
    }
  }

  const loadTransactions = async () => {
    try {
      const result = await CommissionSystemService.getCommissionTransactionsWithFilters(
        filters,
        currentPage,
        20
      )

      setTransactions(result.transactions)
      setTotalPages(Math.ceil(result.total / 20))
    } catch (error) {
      console.error('Error loading transactions:', error)
    }
  }

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }))
    setCurrentPage(1)
  }

  const exportReport = async () => {
    try {
      // Implement CSV export functionality
      const csvData = generateCSVData()
      downloadCSV(csvData, 'commission-report.csv')
    } catch (error) {
      console.error('Error exporting report:', error)
    }
  }

  const generateCSVData = () => {
    // Generate CSV data from transactions
    const headers = ['Date', 'Transaction ID', 'User', 'Beneficiary', 'Type', 'Level', 'Amount', 'Status']
    const rows = transactions.map(t => [
      new Date(t.created_at).toLocaleDateString(),
      t.transaction_id,
      t.user_id,
      t.beneficiary_id,
      t.commission_type,
      t.commission_level.toString(),
      t.commission_amount.toString(),
      t.status
    ])
    
    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }

  const downloadCSV = (csvData: string, filename: string) => {
    const blob = new Blob([csvData], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
  }

  const StatCard = ({ title, value, icon: Icon, color, change }: {
    title: string
    value: number | string
    icon: any
    color: string
    change?: string
  }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-2">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          {change && (
            <p className="text-sm text-green-600 mt-1">{change}</p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
        </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <BarChart3 className="h-8 w-8 text-primary-blue" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Commission Reports & Analytics</h1>
              <p className="text-gray-600">Track commission distributions and user performance</p>
            </div>
          </div>
          <Button onClick={exportReport} className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Export Report</span>
          </Button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Commissions Paid"
            value={`Rs ${reportData.totalCommissionsPaid.toLocaleString()}`}
            icon={DollarSign}
            color="bg-green-500"
            change="+12% vs last month"
          />
          <StatCard
            title="Pending Commissions"
            value={`Rs ${reportData.pendingCommissions.toLocaleString()}`}
            icon={TrendingUp}
            color="bg-yellow-500"
          />
          <StatCard
            title="Failed Commissions"
            value={reportData.failedCommissions}
            icon={Users}
            color="bg-red-500"
          />
          <StatCard
            title="Total Transactions"
            value={reportData.totalTransactions}
            icon={BarChart3}
            color="bg-blue-500"
          />
        </div>

        {/* Top Earners */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Top Commission Earners</h2>
          </div>
          <div className="p-6">
            {reportData.topEarners.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No commission earners found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {reportData.topEarners.map((earner, index) => (
                  <div key={earner.userId} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{earner.fullName}</p>
                        <p className="text-sm text-gray-500">User ID: {earner.userId.slice(0, 8)}...</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">Rs {earner.totalEarned.toLocaleString()}</p>
                      <p className="text-sm text-gray-500">Total Earned</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Filter className="h-5 w-5 text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-900">Transaction Filters</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Input
              label="From Date"
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              fullWidth
            />
            <Input
              label="To Date"
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              fullWidth
            />
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              >
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="processed">Processed</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Commission Type</label>
              <select
                value={filters.commissionType}
                onChange={(e) => handleFilterChange('commissionType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              >
                <option value="">All Types</option>
                <option value="direct_commission">Direct Commission</option>
                <option value="level_commission">Level Commission</option>
                <option value="rsm_bonus">RSM Bonus</option>
                <option value="zm_bonus">ZM Bonus</option>
                <option value="okdoi_head">OKDOI Head</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
              <select
                value={filters.level}
                onChange={(e) => handleFilterChange('level', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              >
                <option value="">All Levels</option>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(level => (
                  <option key={level} value={level.toString()}>Level {level}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Analytics Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Level Distribution */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Commission by Level</h2>
            </div>
            <div className="p-6">
              {reportData.levelDistribution.length === 0 ? (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No level distribution data</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reportData.levelDistribution.map((level) => (
                    <div key={level.level} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-bold text-blue-600">{level.level}</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900">Level {level.level}</span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold text-gray-900">Rs {level.amount.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{level.count} transactions</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Type Distribution */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Commission by Type</h2>
            </div>
            <div className="p-6">
              {reportData.typeDistribution.length === 0 ? (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No type distribution data</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reportData.typeDistribution.map((type) => (
                    <div key={type.type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-gray-900">
                          {type.type.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold text-gray-900">Rs {type.amount.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{type.count} transactions</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Transaction Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Commission Transactions</h2>
          </div>
          <div className="overflow-x-auto">
            {transactions.length === 0 ? (
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No transactions found</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transaction ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Level
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                        {transaction.transaction_id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(transaction.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.commission_type.replace('_', ' ').toUpperCase()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        Level {transaction.commission_level}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        Rs {transaction.commission_amount.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          transaction.status === 'processed'
                            ? 'bg-green-100 text-green-800'
                            : transaction.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : transaction.status === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button className="text-primary-blue hover:text-primary-blue/80">
                          <Eye className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
  )
}
