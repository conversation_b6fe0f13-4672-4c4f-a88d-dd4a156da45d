'use client'

import React, { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Package,
  Calendar,
  BarChart3,
  Pie<PERSON><PERSON>
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import SubscriptionCleanupButton from '@/components/admin/SubscriptionCleanupButton'

interface AnalyticsData {
  totalRevenue: number
  activeSubscriptions: number
  totalSubscriptions: number
  expiredSubscriptions: number
  packageStats: Array<{
    package_name: string
    count: number
    revenue: number
  }>
  monthlyRevenue: Array<{
    month: string
    revenue: number
    subscriptions: number
  }>
}

export default function SubscriptionAnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  useEffect(() => {
    loadAnalytics()
  }, [timeRange])

  const loadAnalytics = async () => {
    try {
      setLoading(true)

      // Calculate date range
      const endDate = new Date()
      const startDate = new Date()
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      // Get subscription statistics
      const [
        { data: subscriptions },
        { data: packages },
        { data: transactions }
      ] = await Promise.all([
        supabase
          .from('user_subscriptions')
          .select(`
            *,
            package:subscription_packages(name, price)
          `)
          .gte('created_at', startDate.toISOString()),
        
        supabase
          .from('subscription_packages')
          .select('*'),
        
        supabase
          .from('wallet_transactions')
          .select('*')
          .eq('transaction_type', 'purchase')
          .eq('reference_type', 'subscription')
          .gte('created_at', startDate.toISOString())
      ])

      // Calculate analytics
      const totalRevenue = transactions?.reduce((sum, t) => sum + Math.abs(t.amount), 0) || 0
      const activeSubscriptions = subscriptions?.filter(s => 
        s.status === 'active' && new Date(s.expires_at) > new Date()
      ).length || 0
      const totalSubscriptions = subscriptions?.length || 0
      const expiredSubscriptions = subscriptions?.filter(s => 
        s.status === 'expired' || new Date(s.expires_at) <= new Date()
      ).length || 0

      // Package statistics
      const packageStats = packages?.map(pkg => {
        const pkgSubscriptions = subscriptions?.filter(s => s.package_id === pkg.id) || []
        return {
          package_name: pkg.name,
          count: pkgSubscriptions.length,
          revenue: pkgSubscriptions.reduce((sum, s) => sum + pkg.price, 0)
        }
      }) || []

      // Monthly revenue (simplified for demo)
      const monthlyRevenue = []
      for (let i = 5; i >= 0; i--) {
        const date = new Date()
        date.setMonth(date.getMonth() - i)
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)
        
        const monthTransactions = transactions?.filter(t => {
          const tDate = new Date(t.created_at)
          return tDate >= monthStart && tDate <= monthEnd
        }) || []
        
        const monthSubscriptions = subscriptions?.filter(s => {
          const sDate = new Date(s.created_at)
          return sDate >= monthStart && sDate <= monthEnd
        }) || []

        monthlyRevenue.push({
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          revenue: monthTransactions.reduce((sum, t) => sum + Math.abs(t.amount), 0),
          subscriptions: monthSubscriptions.length
        })
      }

      setAnalytics({
        totalRevenue,
        activeSubscriptions,
        totalSubscriptions,
        expiredSubscriptions,
        packageStats,
        monthlyRevenue
      })
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
        </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Subscription Analytics</h1>
            <p className="text-gray-600">Monitor subscription performance and revenue</p>
          </div>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  Rs {analytics?.totalRevenue.toLocaleString() || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics?.activeSubscriptions || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Package className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics?.totalSubscriptions || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expired</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analytics?.expiredSubscriptions || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Revenue Chart */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <BarChart3 className="h-5 w-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Monthly Revenue</h3>
            </div>
            <div className="space-y-4">
              {analytics?.monthlyRevenue.map((month, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-700 w-16">
                      {month.month}
                    </span>
                    <div className="ml-4 flex-1">
                      <div className="bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${Math.min((month.revenue / Math.max(...analytics.monthlyRevenue.map(m => m.revenue))) * 100, 100)}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right ml-4">
                    <p className="text-sm font-semibold text-gray-900">
                      Rs {month.revenue.toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-500">
                      {month.subscriptions} subs
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Package Performance */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <PieChart className="h-5 w-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Package Performance</h3>
            </div>
            <div className="space-y-4">
              {analytics?.packageStats.map((pkg, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-3"
                      style={{
                        backgroundColor: `hsl(${index * 60}, 70%, 50%)`
                      }}
                    ></div>
                    <span className="text-sm font-medium text-gray-700">
                      {pkg.package_name}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-gray-900">
                      {pkg.count} subscriptions
                    </p>
                    <p className="text-xs text-gray-500">
                      Rs {pkg.revenue.toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* System Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Cleanup Jobs */}
          <SubscriptionCleanupButton />

          {/* Recent Subscriptions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Subscriptions</h3>
            </div>
            <div className="p-6">
              <div className="text-center py-8 text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Recent subscriptions table would go here</p>
                <p className="text-sm">This would show the latest subscription purchases with user details</p>
              </div>
            </div>
          </div>
        </div>
      </div>
  )
}
