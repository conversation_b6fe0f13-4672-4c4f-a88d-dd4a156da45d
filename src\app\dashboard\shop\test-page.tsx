'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'

export default function TestShopPage() {
  const { user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!authLoading) {
      setLoading(false)
    }
  }, [authLoading])

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
        </div>
    )
  }

  if (!user) {
    return (
      <div className="text-center py-12">
          <div className="text-red-600 mb-4">Please sign in to access your shop</div>
        </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
          <div className="text-red-600 mb-4">{error}</div>
        </div>
    )
  }

  return (
    <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Shop - Test Page</h1>
          <p className="text-gray-600">Testing basic functionality</p>
          <p className="text-sm text-gray-500">User: {user.email}</p>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold mb-4">Basic Test</h2>
          <p>If you can see this, the basic page structure is working.</p>
        </div>
      </div>
  )
}
