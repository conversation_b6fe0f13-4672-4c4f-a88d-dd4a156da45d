'use client'

import React, { useState, useEffect } from 'react'
import {
  Settings,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  DollarSign,
  TrendingUp,
  Users,
  Crown,
  Shield,
  Network,
  ChevronDown,
  ChevronUp,
  Gift,
  Heart,
  GraduationCap,
  CreditCard,
  Car,
  Phone,
  Building
} from 'lucide-react'
import { ExtendedCommissionStructure } from '@/types'

// Commission groups for better organization
const COMMISSION_GROUPS = {
  base: {
    title: 'Base Commissions',
    icon: DollarSign,
    color: 'blue',
    fields: [
      { key: 'direct_commission_rate', label: 'Direct Commission', icon: TrendingUp },
      { key: 'level_commission_rate', label: 'Level Commission', icon: Network }
    ]
  },
  benefits: {
    title: 'Member Benefits',
    icon: Gift,
    color: 'green',
    fields: [
      { key: 'voucher_rate', label: 'Voucher', icon: Gift },
      { key: 'festival_bonus_rate', label: 'Festival Bonus', icon: Crown },
      { key: 'saving_rate', label: 'Saving', icon: Shield },
      { key: 'gift_center_rate', label: 'Gift Center', icon: Gift }
    ]
  },
  services: {
    title: 'Service Commissions',
    icon: Heart,
    color: 'purple',
    fields: [
      { key: 'entertainment_rate', label: 'Entertainment', icon: Heart },
      { key: 'medical_rate', label: 'Medical', icon: Heart },
      { key: 'education_rate', label: 'Education', icon: GraduationCap },
      { key: 'credit_rate', label: 'Credit', icon: CreditCard }
    ]
  },
  zm: {
    title: 'ZM Benefits',
    icon: Crown,
    color: 'amber',
    fields: [
      { key: 'zm_bonus_rate', label: 'ZM Bonus', icon: Crown },
      { key: 'zm_petral_allowance_rate', label: 'Petrol Allowance', icon: Car },
      { key: 'zm_leasing_facility_rate', label: 'Leasing Facility', icon: Building },
      { key: 'zm_phone_bill_rate', label: 'Phone & Bill', icon: Phone }
    ]
  },
  rsm: {
    title: 'RSM Benefits',
    icon: Shield,
    color: 'indigo',
    fields: [
      { key: 'rsm_bonus_rate', label: 'RSM Bonus', icon: Shield },
      { key: 'rsm_petral_allowance_rate', label: 'Petrol Allowance', icon: Car },
      { key: 'rsm_leasing_facility_rate', label: 'Leasing Facility', icon: Building },
      { key: 'rsm_phone_bill_rate', label: 'Phone & Bill', icon: Phone }
    ]
  },
  leadership: {
    title: 'Leadership Bonuses',
    icon: Users,
    color: 'red',
    fields: [
      { key: 'zm_present_leader_rate', label: 'ZM Present Leader', icon: Users },
      { key: 'rsm_present_leader_rate', label: 'RSM Present Leader', icon: Users },
      { key: 'annual_present_leader_rate', label: 'Annual Present Leader', icon: Crown },
      { key: 'okdoi_head_rate', label: 'OKDOI Head', icon: Crown }
    ]
  }
}

export default function CommissionStructurePage() {
  const [structures, setStructures] = useState<ExtendedCommissionStructure[]>([])
  const [loading, setLoading] = useState(true)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({
    base: true,
    benefits: false,
    services: false,
    zm: false,
    rsm: false,
    leadership: false
  })
  const [selectedPackageValue, setSelectedPackageValue] = useState<number | null>(null)
  const [formData, setFormData] = useState<Partial<ExtendedCommissionStructure>>({
    commission_type: 'subscription',
    package_value: 0,
    direct_commission_rate: 0.10,
    level_commission_rate: 0.02,
    voucher_rate: 0.01,
    festival_bonus_rate: 0.01,
    saving_rate: 0.01,
    gift_center_rate: 0.005,
    entertainment_rate: 0.002,
    medical_rate: 0.001,
    education_rate: 0.001,
    credit_rate: 0.001,
    zm_bonus_rate: 0.05,
    zm_petral_allowance_rate: 0.005,
    zm_leasing_facility_rate: 0.01,
    zm_phone_bill_rate: 0.001,
    rsm_bonus_rate: 0.05,
    rsm_petral_allowance_rate: 0.005,
    rsm_leasing_facility_rate: 0.01,
    rsm_phone_bill_rate: 0.001,
    zm_present_leader_rate: 0.02,
    rsm_present_leader_rate: 0.01,
    annual_present_leader_rate: 0.01,
    okdoi_head_rate: 0.02,
    is_active: true
  })

  useEffect(() => {
    fetchCommissionStructures()
  }, [])

  const fetchCommissionStructures = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/extended-commission-structure')
      const data = await response.json()

      if (data.success) {
        setStructures(data.data)
      } else {
        alert('Error fetching commission structures: ' + data.error)
      }
    } catch (error) {
      console.error('Error fetching commission structures:', error)
      alert('Failed to fetch commission structures')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (id?: string) => {
    try {
      const url = id
        ? `/api/admin/extended-commission-structure/${id}`
        : '/api/admin/extended-commission-structure'

      const method = id ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (data.success) {
        await fetchCommissionStructures()
        setEditingId(null)
        setShowAddForm(false)
        resetForm()
        alert(id ? 'Commission structure updated successfully!' : 'Commission structure created successfully!')
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving commission structure:', error)
      alert('Failed to save commission structure')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this commission structure?')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/extended-commission-structure/${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        await fetchCommissionStructures()
        alert('Commission structure deleted successfully!')
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting commission structure:', error)
      alert('Failed to delete commission structure')
    }
  }

  const startEdit = (structure: ExtendedCommissionStructure) => {
    setFormData(structure)
    setEditingId(structure.id)
    setShowAddForm(false)
  }

  const resetForm = () => {
    setFormData({
      commission_type: 'subscription',
      package_value: 0,
      direct_commission_rate: 0.10,
      level_commission_rate: 0.02,
      is_active: true
    })
  }

  const cancelEdit = () => {
    setEditingId(null)
    setShowAddForm(false)
    resetForm()
  }

  const handleInputChange = (field: keyof ExtendedCommissionStructure, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatPercentage = (rate: number) => {
    return (rate * 100).toFixed(2) + '%'
  }

  const formatCurrency = (amount: number) => {
    return `Rs ${amount.toLocaleString()}`
  }

  const toggleGroup = (groupKey: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupKey]: !prev[groupKey]
    }))
  }

  const getUniquePackageValues = () => {
    const values = [...new Set(structures.map(s => s.package_value))].sort((a, b) => a - b)
    return values
  }

  const getStructureByPackageValue = (packageValue: number) => {
    return structures.find(s => s.package_value === packageValue)
  }

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 border-blue-200 text-blue-900',
      green: 'bg-green-50 border-green-200 text-green-900',
      purple: 'bg-purple-50 border-purple-200 text-purple-900',
      amber: 'bg-amber-50 border-amber-200 text-amber-900',
      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-900',
      red: 'bg-red-50 border-red-200 text-red-900'
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue mx-auto mb-4"></div>
            <p className="text-gray-600">Loading commission structures...</p>
          </div>
        </div>
    )
  }

  return (
    <div className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Commission Structure Management</h1>
                <p className="text-gray-600">Manage commission rates for different package values</p>
              </div>
            </div>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Structure</span>
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Structures</p>
                  <p className="text-2xl font-bold text-gray-900">{structures.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Structures</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {structures.filter(s => s.is_active).length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Package Types</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Set(structures.map(s => s.commission_type)).size}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                  <Crown className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Direct Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(
                      structures.reduce((sum, s) => sum + s.direct_commission_rate, 0) / structures.length || 0
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Package Value Selector */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Select Package Value</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {getUniquePackageValues().map((packageValue) => (
                <button
                  key={packageValue}
                  onClick={() => setSelectedPackageValue(packageValue)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedPackageValue === packageValue
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  <div className="text-sm font-medium">{formatCurrency(packageValue)}</div>
                  <div className="text-xs text-gray-500 mt-1">Package</div>
                </button>
              ))}
            </div>
          </div>

          {/* Commission Groups */}
          {selectedPackageValue && (
            <div className="space-y-6">
              {Object.entries(COMMISSION_GROUPS).map(([groupKey, group]) => {
                const structure = getStructureByPackageValue(selectedPackageValue)
                const isExpanded = expandedGroups[groupKey]
                const IconComponent = group.icon

                return (
                  <div key={groupKey} className={`bg-white rounded-lg shadow-sm border-2 ${getColorClasses(group.color)}`}>
                    <div
                      className="px-6 py-4 cursor-pointer"
                      onClick={() => toggleGroup(groupKey)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center bg-${group.color}-100`}>
                            <IconComponent className={`h-5 w-5 text-${group.color}-600`} />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold">{group.title}</h3>
                            <p className="text-sm opacity-75">{group.fields.length} commission types</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">
                            {formatCurrency(selectedPackageValue)} Package
                          </span>
                          {isExpanded ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </div>
                      </div>
                    </div>

                    {isExpanded && structure && (
                      <div className="px-6 pb-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {group.fields.map((field) => {
                            const FieldIcon = field.icon
                            const value = structure[field.key as keyof ExtendedCommissionStructure] as number

                            return (
                              <div key={field.key} className="bg-white rounded-lg border border-gray-200 p-4">
                                <div className="flex items-center space-x-3 mb-2">
                                  <div className={`w-8 h-8 rounded-lg bg-${group.color}-100 flex items-center justify-center`}>
                                    <FieldIcon className={`h-4 w-4 text-${group.color}-600`} />
                                  </div>
                                  <div>
                                    <h4 className="text-sm font-medium text-gray-900">{field.label}</h4>
                                    <p className="text-xs text-gray-500">{field.key}</p>
                                  </div>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="text-lg font-bold text-gray-900">
                                    {formatPercentage(value || 0)}
                                  </span>
                                  <button
                                    onClick={() => startEdit(structure)}
                                    className="text-blue-600 hover:text-blue-800 p-1"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </button>
                                </div>
                              </div>
                            )
                          })}
                        </div>

                        <div className="mt-4 flex items-center justify-between pt-4 border-t border-gray-200">
                          <div className="flex items-center space-x-4">
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                              structure.is_active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {structure.is_active ? 'Active' : 'Inactive'}
                            </span>
                            <span className="text-sm text-gray-500">
                              Type: {structure.commission_type}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => startEdit(structure)}
                              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                            >
                              <Edit className="h-4 w-4" />
                              <span>Edit All</span>
                            </button>
                            <button
                              onClick={() => handleDelete(structure.id)}
                              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span>Delete</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          )}

          {!selectedPackageValue && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Settings className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Package Value</h3>
              <p className="text-gray-600">Choose a package value above to view and manage its commission structure.</p>
            </div>
          )}

          {/* Edit Form Modal */}
          {(editingId || showAddForm) && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-900">
                      {editingId ? 'Edit Commission Structure' : 'Add Commission Structure'}
                    </h2>
                    <button
                      onClick={cancelEdit}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>
                </div>

                <div className="p-6">
                  {/* Basic Information */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Package Value (Rs)
                        </label>
                        <input
                          type="number"
                          value={formData.package_value || ''}
                          onChange={(e) => handleInputChange('package_value', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter package value"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Commission Type
                        </label>
                        <select
                          value={formData.commission_type || 'subscription'}
                          onChange={(e) => handleInputChange('commission_type', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="subscription">Subscription</option>
                          <option value="direct_commission">Direct Commission</option>
                          <option value="level_commission">Level Commission</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Status
                        </label>
                        <select
                          value={formData.is_active ? 'true' : 'false'}
                          onChange={(e) => handleInputChange('is_active', e.target.value === 'true')}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="true">Active</option>
                          <option value="false">Inactive</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Commission Groups */}
                  <div className="space-y-6">
                    {Object.entries(COMMISSION_GROUPS).map(([groupKey, group]) => {
                      const IconComponent = group.icon

                      return (
                        <div key={groupKey} className={`border-2 rounded-lg ${getColorClasses(group.color)}`}>
                          <div className="px-4 py-3 border-b border-gray-200">
                            <div className="flex items-center space-x-3">
                              <div className={`w-8 h-8 rounded-lg bg-${group.color}-100 flex items-center justify-center`}>
                                <IconComponent className={`h-4 w-4 text-${group.color}-600`} />
                              </div>
                              <h4 className="text-lg font-medium">{group.title}</h4>
                            </div>
                          </div>
                          <div className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {group.fields.map((field) => {
                                const FieldIcon = field.icon

                                return (
                                  <div key={field.key}>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                      <div className="flex items-center space-x-2">
                                        <FieldIcon className="h-4 w-4" />
                                        <span>{field.label}</span>
                                      </div>
                                    </label>
                                    <div className="relative">
                                      <input
                                        type="number"
                                        step="0.0001"
                                        min="0"
                                        max="1"
                                        value={formData[field.key as keyof ExtendedCommissionStructure] as number || 0}
                                        onChange={(e) => handleInputChange(field.key as keyof ExtendedCommissionStructure, parseFloat(e.target.value) || 0)}
                                        className="w-full px-3 py-2 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.0000"
                                      />
                                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <span className="text-sm text-gray-500">%</span>
                                      </div>
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                      {formatPercentage((formData[field.key as keyof ExtendedCommissionStructure] as number) || 0)}
                                    </p>
                                  </div>
                                )
                              })}
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  {/* Form Actions */}
                  <div className="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                    <button
                      onClick={cancelEdit}
                      className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => handleSave(editingId || undefined)}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                    >
                      <Save className="h-4 w-4" />
                      <span>{editingId ? 'Update' : 'Create'} Structure</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
  )
}


