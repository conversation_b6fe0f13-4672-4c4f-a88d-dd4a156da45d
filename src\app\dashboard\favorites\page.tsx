'use client'

import React, { useState, useEffect } from 'react'
import { Heart, Trash2, Search, Grid, List, Filter } from 'lucide-react'
import AdGrid from '@/components/ads/AdGrid'
import { FavoritesService } from '@/lib/services/favorites'
import { AdWithDetails } from '@/types'
import { useAuth } from '@/contexts/AuthContext'
import { showConfirmation, showAlert } from '@/components/ui/ConfirmationDialog'

export default function FavoritesPage() {
  const { user } = useAuth()
  const [favorites, setFavorites] = useState<AdWithDetails[]>([])
  const [filteredFavorites, setFilteredFavorites] = useState<AdWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedFavorites, setSelectedFavorites] = useState<Set<string>>(new Set())
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'price_low' | 'price_high'>('newest')

  useEffect(() => {
    if (user) {
      loadFavorites()
    }
  }, [user])

  useEffect(() => {
    filterAndSortFavorites()
  }, [favorites, searchTerm, sortBy])

  const loadFavorites = async () => {
    if (!user) return

    try {
      setLoading(true)
      const userFavorites = await FavoritesService.getUserFavorites(user.id)
      setFavorites(userFavorites)
    } catch (error) {
      console.error('Error loading favorites:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to load your favorites',
        variant: 'danger'
      })
    } finally {
      setLoading(false)
    }
  }

  const filterAndSortFavorites = () => {
    let filtered = [...favorites]

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(ad =>
        ad.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ad.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ad.location?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        case 'price_low':
          return a.price - b.price
        case 'price_high':
          return b.price - a.price
        default:
          return 0
      }
    })

    setFilteredFavorites(filtered)
  }

  const handleRemoveFromFavorites = async (adId: string) => {
    if (!user) return

    const confirmed = await showConfirmation({
      title: 'Remove from Favorites',
      message: 'Are you sure you want to remove this ad from your favorites?',
      confirmText: 'Remove',
      variant: 'danger'
    })

    if (confirmed) {
      try {
        await FavoritesService.removeFromFavorites(user.id, adId)
        setFavorites(prev => prev.filter(ad => ad.id !== adId))
        await showAlert({
          title: 'Success',
          message: 'Ad removed from favorites',
          variant: 'success'
        })
      } catch (error) {
        console.error('Error removing from favorites:', error)
        await showAlert({
          title: 'Error',
          message: 'Failed to remove ad from favorites',
          variant: 'danger'
        })
      }
    }
  }

  const handleBulkRemove = async () => {
    if (!user || selectedFavorites.size === 0) return

    const confirmed = await showConfirmation({
      title: 'Remove Selected Favorites',
      message: `Are you sure you want to remove ${selectedFavorites.size} ads from your favorites?`,
      confirmText: 'Remove All',
      variant: 'danger'
    })

    if (confirmed) {
      try {
        await FavoritesService.removeMultipleFromFavorites(user.id, Array.from(selectedFavorites))
        setFavorites(prev => prev.filter(ad => !selectedFavorites.has(ad.id)))
        setSelectedFavorites(new Set())
        await showAlert({
          title: 'Success',
          message: `${selectedFavorites.size} ads removed from favorites`,
          variant: 'success'
        })
      } catch (error) {
        console.error('Error removing favorites:', error)
        await showAlert({
          title: 'Error',
          message: 'Failed to remove ads from favorites',
          variant: 'danger'
        })
      }
    }
  }

  const toggleSelectFavorite = (adId: string) => {
    setSelectedFavorites(prev => {
      const newSet = new Set(prev)
      if (newSet.has(adId)) {
        newSet.delete(adId)
      } else {
        newSet.add(adId)
      }
      return newSet
    })
  }

  const selectAllFavorites = () => {
    if (selectedFavorites.size === filteredFavorites.length) {
      setSelectedFavorites(new Set())
    } else {
      setSelectedFavorites(new Set(filteredFavorites.map(ad => ad.id)))
    }
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Heart className="h-6 w-6 mr-2 text-red-500" />
              My Favorites
            </h1>
            <p className="text-gray-600 mt-1">
              {favorites.length} {favorites.length === 1 ? 'favorite ad' : 'favorite ads'}
            </p>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid'
                  ? 'bg-primary-blue text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list'
                  ? 'bg-primary-blue text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search your favorites..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              />
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="price_low">Price: Low to High</option>
              <option value="price_high">Price: High to Low</option>
            </select>
          </div>

          {/* Bulk Actions */}
          {selectedFavorites.size > 0 && (
            <div className="mt-4 flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span className="text-sm text-blue-700">
                {selectedFavorites.size} ads selected
              </span>
              <button
                onClick={handleBulkRemove}
                className="flex items-center px-3 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Remove Selected
              </button>
            </div>
          )}
        </div>

        {/* Favorites Grid */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          {loading ? (
            <AdGrid ads={[]} loading={true} viewMode={viewMode} />
          ) : filteredFavorites.length === 0 ? (
            <div className="text-center py-12">
              <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {searchTerm ? 'No matching favorites' : 'No favorites yet'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm
                  ? 'Try adjusting your search terms'
                  : 'Start browsing ads and add them to your favorites'}
              </p>
              {!searchTerm && (
                <a
                  href="/ads"
                  className="inline-flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                >
                  Browse Ads
                </a>
              )}
            </div>
          ) : (
            <AdGrid
              ads={filteredFavorites}
              viewMode={viewMode}
              showFeaturedBadge={true}
            />
          )}
        </div>
      </div>
  )
}
