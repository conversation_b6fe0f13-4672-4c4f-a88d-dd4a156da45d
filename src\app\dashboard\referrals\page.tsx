'use client'

import React, { useState, useEffect } from 'react'
import {
  Gift,
  Users,
  Copy,
  Share2,
  DollarSign,
  TrendingUp,
  Calendar,
  CheckCircle,
  ExternalLink,
  Network,
  Eye
} from 'lucide-react'
import But<PERSON> from '@/components/ui/Button'
import { useAuth } from '@/contexts/AuthContext'
import { ReferralSystemService, ReferralStats, ReferralTreeNode } from '@/lib/services/referralSystem'
import { CommissionSystemService } from '@/lib/services/commissionSystem'
import { CommissionTransaction } from '@/types'

export default function ReferralsPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState<ReferralStats>({
    directReferrals: 0,
    totalDownline: 0,
    totalCommissionEarned: 0,
    currentLevel: 0
  })
  const [commissions, setCommissions] = useState<CommissionTransaction[]>([])
  const [copied, setCopied] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const referralLink = user && stats.referralCode ? `${typeof window !== 'undefined' ? window.location.origin : 'https://okdoi.com'}/auth/signup?ref=${stats.referralCode}` : ''

  useEffect(() => {
    if (user) {
      loadReferralData()
    }
  }, [user])

  const loadReferralData = async () => {
    if (!user) return

    try {
      setLoading(true)

      const [statsData, commissionsData] = await Promise.all([
        ReferralSystemService.getUserReferralStats(user.id),
        CommissionSystemService.getUserCommissionTransactions(user.id, {}, 1, 10)
      ])

      setStats(statsData)
      setCommissions(commissionsData.transactions)
    } catch (error) {
      console.error('Error loading referral data:', error)
      setError('Failed to load referral data')
    } finally {
      setLoading(false)
    }
  }

  const copyReferralLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  const shareReferralLink = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join OKDOI - Premium Marketplace',
          text: 'Join me on OKDOI and get exclusive benefits!',
          url: referralLink
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      copyReferralLink()
    }
  }



  if (loading) {
    return (
      <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        </div>
    )
  }

  return (
    <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-8 text-white">
          <div className="flex items-center mb-4">
            <Gift className="h-8 w-8 mr-3" />
            <h1 className="text-3xl font-bold">Referral Program</h1>
          </div>
          <p className="text-purple-100 text-lg">
            Earn rewards by inviting friends to join OKDOI. Get Rs 100 for each successful referral!
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Direct Referrals</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  {stats.directReferrals}
                </p>
              </div>
              <div className="p-3 rounded-lg bg-blue-500">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Commission Earned</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  Rs {stats.totalCommissionEarned.toLocaleString()}
                </p>
              </div>
              <div className="p-3 rounded-lg bg-green-500">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Downline</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  {stats.totalDownline}
                </p>
              </div>
              <div className="p-3 rounded-lg bg-orange-500">
                <Calendar className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Current Level</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  {stats.currentLevel}
                </p>
              </div>
              <div className="p-3 rounded-lg bg-purple-500">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Referral Link */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Referral Link</h3>
              {referralLink ? (
                <>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="flex-1 p-3 bg-gray-50 rounded-lg border">
                      <p className="text-sm text-gray-600 break-all">{referralLink}</p>
                    </div>
                    <button
                      onClick={copyReferralLink}
                      className="flex items-center px-4 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                    >
                      {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                    <button
                      onClick={shareReferralLink}
                      className="flex items-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <Share2 className="h-4 w-4" />
                    </button>
                  </div>
                  {copied && (
                    <p className="text-sm text-green-600 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Link copied to clipboard!
                    </p>
                  )}
                </>
              ) : (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    Loading your referral link...
                  </p>
                </div>
              )}
            </div>

            {/* How it Works */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">How It Works</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-primary-blue/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-bold text-primary-blue">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Share Your Link</h4>
                    <p className="text-sm text-gray-600">Send your unique referral link to friends and family</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-primary-blue/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-bold text-primary-blue">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">They Sign Up</h4>
                    <p className="text-sm text-gray-600">Your friends create an account using your link</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-primary-blue/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-bold text-primary-blue">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Earn Rewards</h4>
                    <p className="text-sm text-gray-600">Get Rs 100 when they post their first ad</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Referral History */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Referrals</h3>
              <div className="space-y-3">
                {stats.totalReferrals > 0 ? (
                  <>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-gray-900">John D.</p>
                        <p className="text-xs text-gray-500">2 days ago</p>
                      </div>
                      <span className="text-sm font-medium text-green-600">+Rs 100</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-gray-900">Sarah M.</p>
                        <p className="text-xs text-gray-500">1 week ago</p>
                      </div>
                      <span className="text-sm font-medium text-green-600">+Rs 100</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-gray-900">Mike R.</p>
                        <p className="text-xs text-gray-500">Pending verification</p>
                      </div>
                      <span className="text-sm font-medium text-yellow-600">Pending</span>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-sm text-gray-500">No referrals yet</p>
                    <p className="text-xs text-gray-400">Start sharing your link to earn rewards!</p>
                  </div>
                )}
              </div>
            </div>

            {/* Terms */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Terms & Conditions</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <p>• Earn Rs 100 for each successful referral</p>
                <p>• Referred user must post their first ad</p>
                <p>• Rewards are credited within 7 days</p>
                <p>• Minimum withdrawal amount is Rs 500</p>
                <p>• Self-referrals are not allowed</p>
              </div>
              <a
                href="/terms"
                className="inline-flex items-center text-primary-blue hover:text-primary-blue/80 font-medium text-sm mt-3"
              >
                View Full Terms
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          </div>
        </div>



        {/* Commission History */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Commission History</h2>
          </div>
          <div className="p-6">
            {commissions.length === 0 ? (
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No commissions earned yet</p>
                <p className="text-sm text-gray-400 mt-2">
                  Start referring friends to earn commissions!
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Level
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {commissions.map((commission) => (
                      <tr key={commission.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(commission.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {commission.commission_type.replace('_', ' ').toUpperCase()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          Level {commission.commission_level}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          Rs {commission.commission_amount.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            commission.status === 'processed'
                              ? 'bg-green-100 text-green-800'
                              : commission.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {commission.status.charAt(0).toUpperCase() + commission.status.slice(1)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
  )
}
