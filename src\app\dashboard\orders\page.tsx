'use client'

import React, { useState, useEffect } from 'react'
import {
  Package,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  Eye,
  Search,
  Filter,
  Calendar,
  MapPin,
  Phone,
  Mail,
  ExternalLink,
  Star
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import ProductReviewModal from '@/components/reviews/ProductReviewModal'
import { useAuth } from '@/contexts/AuthContext'
import { OrderService } from '@/lib/services/orders'
import { ShopOrder } from '@/types'
import { formatCurrency } from '@/lib/utils'
import { showAlert, showConfirmation } from '@/components/ui/ConfirmationDialog'

export default function MyOrdersPage() {
  const { user } = useAuth()
  const [orders, setOrders] = useState<ShopOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalOrders, setTotalOrders] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedOrder, setSelectedOrder] = useState<ShopOrder | null>(null)
  const [showOrderDetails, setShowOrderDetails] = useState(false)
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [reviewableProducts, setReviewableProducts] = useState<any[]>([])
  const [selectedOrderForReview, setSelectedOrderForReview] = useState<ShopOrder | null>(null)

  const ordersPerPage = 10

  useEffect(() => {
    if (user) {
      loadOrders()
    }
  }, [user, currentPage, statusFilter])

  const loadOrders = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      const { orders: fetchedOrders, total } = await OrderService.getBuyerOrders(
        user.id,
        currentPage,
        ordersPerPage
      )

      // Filter by status if needed
      let filteredOrders = fetchedOrders
      if (statusFilter !== 'all') {
        filteredOrders = fetchedOrders.filter(order => order.status === statusFilter)
      }

      // Filter by search query
      if (searchQuery.trim()) {
        filteredOrders = filteredOrders.filter(order =>
          order.order_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.shop?.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      }

      setOrders(filteredOrders)
      setTotalOrders(total)
    } catch (err) {
      console.error('Error loading orders:', err)
      // Only show error if it's a real error, not just empty results
      if (err instanceof Error && !err.message.includes('No orders found')) {
        setError('Failed to load orders. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      case 'pending_shipment':
        return 'text-orange-600 bg-orange-100'
      case 'confirmed':
        return 'text-blue-600 bg-blue-100'
      case 'processing':
        return 'text-purple-600 bg-purple-100'
      case 'shipped':
        return 'text-indigo-600 bg-indigo-100'
      case 'delivered':
        return 'text-green-600 bg-green-100'
      case 'cancelled':
        return 'text-red-600 bg-red-100'
      case 'refunded':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
      case 'pending_shipment':
        return Clock
      case 'confirmed':
      case 'processing':
        return Package
      case 'shipped':
        return Truck
      case 'delivered':
        return CheckCircle
      case 'cancelled':
      case 'refunded':
        return XCircle
      default:
        return Package
    }
  }

  const handleViewOrder = (order: ShopOrder) => {
    setSelectedOrder(order)
    setShowOrderDetails(true)
  }

  const handleConfirmDelivery = async (order: ShopOrder) => {
    const confirmed = await showConfirmation({
      title: 'Confirm Delivery',
      message: 'Have you received your order? This action will mark the order as delivered and credit the merchant.',
      confirmText: 'Confirm Delivery',
      cancelText: 'Not Yet',
      variant: 'success'
    })

    if (!confirmed) return

    try {
      await OrderService.confirmDelivery(order.id, user?.id || '')
      await showAlert({
        title: 'Delivery Confirmed',
        message: 'Thank you! Your order has been marked as delivered.',
        variant: 'success'
      })
      loadOrders() // Refresh orders
    } catch (error) {
      console.error('Error confirming delivery:', error)
      await showAlert({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to confirm delivery',
        variant: 'danger'
      })
    }
  }

  const handleReviewOrder = async (order: ShopOrder) => {
    if (!user) return

    try {
      const { canReview, products } = await OrderService.getReviewableProducts(order.id, user.id)

      if (!canReview) {
        await showAlert({
          title: 'Cannot Review',
          message: 'You can only review products from shipped, delivered or cancelled orders.',
          variant: 'warning'
        })
        return
      }

      if (products.length === 0) {
        await showAlert({
          title: 'No Products to Review',
          message: 'You have already reviewed all products from this order.',
          variant: 'info'
        })
        return
      }

      setReviewableProducts(products)
      setSelectedOrderForReview(order)
      setShowReviewModal(true)
    } catch (error) {
      console.error('Error loading reviewable products:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to load products for review.',
        variant: 'danger'
      })
    }
  }

  const handleReviewSubmitted = () => {
    // Refresh orders to update review status
    loadOrders()
  }

  const totalPages = Math.ceil(totalOrders / ordersPerPage)

  if (loading && orders.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
        </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Orders</h1>
            <p className="text-gray-600">Track and manage your orders</p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by order number or shop name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              >
                <option value="all">All Orders</option>
                <option value="pending">Pending</option>
                <option value="pending_shipment">Pending Shipment</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>

            <button
              onClick={loadOrders}
              className="px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Apply
            </button>
          </div>
        </div>

        {/* Orders List */}
        {error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">{error}</p>
          </div>
        ) : orders.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Orders Found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || statusFilter !== 'all'
                ? 'No orders match your current filters.'
                : "You haven't placed any orders yet."
              }
            </p>
            {!searchQuery && statusFilter === 'all' && (
              <div className="space-y-2">
                <p className="text-sm text-gray-500">Start shopping to see your orders here!</p>
                <Link
                  href="/e-store"
                  className="inline-flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                >
                  Start Shopping
                </Link>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => {
              const StatusIcon = getStatusIcon(order.status)
              
              return (
                <div key={order.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Order #{order.order_number}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Placed on {new Date(order.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                        <StatusIcon className="h-4 w-4 mr-1" />
                        {order.status.replace('_', ' ').charAt(0).toUpperCase() + order.status.replace('_', ' ').slice(1)}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">
                        {formatCurrency(order.total_amount)}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.order_items?.length || 0} item(s)
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Package className="h-4 w-4 mr-1" />
                        {order.shop?.name}
                      </div>
                      {order.tracking_number && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Truck className="h-4 w-4 mr-1" />
                          Tracking: {order.tracking_number}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* Confirm Delivery Button - Only for shipped orders */}
                      {order.status === 'shipped' && (
                        <button
                          onClick={() => handleConfirmDelivery(order)}
                          className="inline-flex items-center px-3 py-2 text-sm font-medium text-green-600 hover:text-green-700 border border-green-600 hover:border-green-700 rounded-lg transition-colors"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Confirm Delivery
                        </button>
                      )}

                      {/* Review Button - For shipped, delivered, or cancelled orders */}
                      {(order.status === 'shipped' || order.status === 'delivered' || order.status === 'cancelled') && (
                        <button
                          onClick={() => handleReviewOrder(order)}
                          className="inline-flex items-center px-3 py-2 text-sm font-medium text-yellow-600 hover:text-yellow-700 border border-yellow-600 hover:border-yellow-700 rounded-lg transition-colors"
                        >
                          <Star className="h-4 w-4 mr-1" />
                          Write Review
                        </button>
                      )}

                      <button
                        onClick={() => handleViewOrder(order)}
                        className="inline-flex items-center px-3 py-2 text-sm font-medium text-primary-blue hover:text-primary-blue/80 border border-primary-blue hover:border-primary-blue/80 rounded-lg transition-colors"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </button>
                    </div>
                  </div>

                  {/* Order Items Preview */}
                  {order.order_items && order.order_items.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex items-center space-x-3">
                        {order.order_items.slice(0, 3).map((item) => (
                          <div key={item.id} className="flex items-center space-x-2">
                            <div className="w-10 h-10 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                              {item.product?.images && item.product.images.length > 0 ? (
                                <Image
                                  src={item.product.images.find(img => img.is_primary)?.image_url || item.product.images[0].image_url}
                                  alt={item.product_title}
                                  width={40}
                                  height={40}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Package className="h-4 w-4 text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-900 truncate max-w-32">
                                {item.product_title}
                              </p>
                              <p className="text-xs text-gray-600">
                                Qty: {item.quantity}
                              </p>
                            </div>
                          </div>
                        ))}
                        {order.order_items.length > 3 && (
                          <div className="text-sm text-gray-600">
                            +{order.order_items.length - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {((currentPage - 1) * ordersPerPage) + 1} to {Math.min(currentPage * ordersPerPage, totalOrders)} of {totalOrders} orders
            </p>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-2 text-sm font-medium text-gray-700">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Order #{selectedOrder.order_number}
                </h2>
                <p className="text-gray-600">
                  Placed on {new Date(selectedOrder.created_at).toLocaleDateString()}
                </p>
              </div>
              <button
                onClick={() => setShowOrderDetails(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <XCircle className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Order Status */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Order Status</h3>
                  <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(selectedOrder.status)}`}>
                    {React.createElement(getStatusIcon(selectedOrder.status), { className: "h-4 w-4 mr-2" })}
                    {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                  </div>
                  
                  {selectedOrder.tracking_number && (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Truck className="h-5 w-5 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          Tracking: {selectedOrder.tracking_number}
                        </span>
                        {selectedOrder.tracking_url && (
                          <a
                            href={selectedOrder.tracking_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-blue hover:text-primary-blue/80"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        )}
                      </div>
                      {selectedOrder.courier_name && (
                        <div className="flex items-center space-x-2">
                          <div className="w-5 h-5" /> {/* Spacer */}
                          <span className="text-sm text-gray-600">
                            via {selectedOrder.courier_name}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Shipping Address */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Shipping Address</h3>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p className="font-medium">{selectedOrder.shipping_address.name}</p>
                    <p>{selectedOrder.shipping_address.address}</p>
                    <p>{selectedOrder.shipping_address.city}, {selectedOrder.shipping_address.district}</p>
                    {selectedOrder.shipping_address.postal_code && (
                      <p>{selectedOrder.shipping_address.postal_code}</p>
                    )}
                    <div className="flex items-center space-x-4 mt-2">
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-1" />
                        {selectedOrder.shipping_address.phone}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                <div className="space-y-4">
                  {selectedOrder.order_items?.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                        {item.product?.images && item.product.images.length > 0 ? (
                          <Image
                            src={item.product.images.find(img => img.is_primary)?.image_url || item.product.images[0].image_url}
                            alt={item.product_title}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.product_title}</h4>
                        {item.product_sku && (
                          <p className="text-sm text-gray-600">SKU: {item.product_sku}</p>
                        )}
                        <p className="text-sm text-gray-600">
                          Quantity: {item.quantity} × {formatCurrency(item.unit_price)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(item.total_price)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Summary */}
              <div className="mt-6 bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal:</span>
                    <span>{formatCurrency(selectedOrder.subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Shipping:</span>
                    <span>{formatCurrency(selectedOrder.shipping_cost)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Tax:</span>
                    <span>{formatCurrency(selectedOrder.tax_amount)}</span>
                  </div>
                  {selectedOrder.discount_amount > 0 && (
                    <div className="flex justify-between text-sm text-green-600">
                      <span>Discount:</span>
                      <span>-{formatCurrency(selectedOrder.discount_amount)}</span>
                    </div>
                  )}
                  <div className="border-t border-gray-200 pt-2">
                    <div className="flex justify-between text-lg font-semibold">
                      <span>Total:</span>
                      <span>{formatCurrency(selectedOrder.total_amount)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Notes */}
              {selectedOrder.buyer_notes && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Order Notes</h3>
                  <p className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                    {selectedOrder.buyer_notes}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Product Review Modal */}
      {showReviewModal && selectedOrderForReview && user && (
        <ProductReviewModal
          isOpen={showReviewModal}
          onClose={() => {
            setShowReviewModal(false)
            setSelectedOrderForReview(null)
            setReviewableProducts([])
          }}
          products={reviewableProducts}
          userId={user.id}
          orderId={selectedOrderForReview.id}
          onReviewSubmitted={handleReviewSubmitted}
        />
      )}
  )
}
