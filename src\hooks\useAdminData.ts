'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

interface UseAdminDataOptions<T> {
  fetchFn: () => Promise<T>
  dependencies?: any[]
  cacheKey?: string
  cacheDuration?: number // in milliseconds
}

interface UseAdminDataReturn<T> {
  data: T | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Simple in-memory cache
const cache = new Map<string, { data: any; timestamp: number }>()

export function useAdminData<T>({
  fetchFn,
  dependencies = [],
  cacheKey,
  cacheDuration = 30000 // 30 seconds default
}: UseAdminDataOptions<T>): UseAdminDataReturn<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchData = useCallback(async () => {
    // Check cache first
    if (cacheKey) {
      const cached = cache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < cacheDuration) {
        setData(cached.data)
        setLoading(false)
        return
      }
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()

    try {
      setLoading(true)
      setError(null)
      
      const result = await fetchFn()
      
      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return
      }

      setData(result)
      
      // Cache the result
      if (cacheKey) {
        cache.set(cacheKey, { data: result, timestamp: Date.now() })
      }
    } catch (err) {
      if (abortControllerRef.current?.signal.aborted) {
        return
      }
      
      setError(err instanceof Error ? err.message : 'An error occurred')
      setData(null)
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false)
      }
    }
  }, [fetchFn, cacheKey, cacheDuration])

  useEffect(() => {
    fetchData()

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, dependencies)

  const refetch = useCallback(async () => {
    // Clear cache for this key
    if (cacheKey) {
      cache.delete(cacheKey)
    }
    await fetchData()
  }, [fetchData, cacheKey])

  return { data, loading, error, refetch }
}

// Clear all cache
export function clearAdminCache() {
  cache.clear()
}

// Clear specific cache key
export function clearAdminCacheKey(key: string) {
  cache.delete(key)
}
