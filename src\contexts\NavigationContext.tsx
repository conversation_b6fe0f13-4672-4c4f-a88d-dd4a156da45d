'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'

interface NavigationContextType {
  isNavigating: boolean
  setIsNavigating: (value: boolean) => void
  navigationProgress: number
  setNavigationProgress: (value: number) => void
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined)

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [isNavigating, setIsNavigating] = useState(false)
  const [navigationProgress, setNavigationProgress] = useState(0)
  const pathname = usePathname()

  useEffect(() => {
    // Reset navigation state when pathname changes
    setIsNavigating(false)
    setNavigationProgress(0)
  }, [pathname])

  return (
    <NavigationContext.Provider value={{
      isNavigating,
      setIsNavigating,
      navigationProgress,
      setNavigationProgress
    }}>
      {children}
    </NavigationContext.Provider>
  )
}

export function useNavigation() {
  const context = useContext(NavigationContext)
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider')
  }
  return context
}
