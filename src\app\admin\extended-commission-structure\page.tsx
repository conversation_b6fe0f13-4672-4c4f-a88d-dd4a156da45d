'use client'

import React, { useState, useEffect } from 'react'
import { 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  DollarSign,
  TrendingUp,
  Users,
  Crown,
  Shield,
  Network
} from 'lucide-react'
import { ExtendedCommissionStructure } from '@/types'

export default function ExtendedCommissionStructurePage() {
  const [structures, setStructures] = useState<ExtendedCommissionStructure[]>([])
  const [loading, setLoading] = useState(true)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState<Partial<ExtendedCommissionStructure>>({
    commission_type: 'subscription',
    package_value: 0,
    direct_commission_rate: 0.10,
    level_commission_rate: 0.02,
    voucher_rate: 0.01,
    festival_bonus_rate: 0.01,
    saving_rate: 0.01,
    gift_center_rate: 0.005,
    entertainment_rate: 0.002,
    medical_rate: 0.001,
    education_rate: 0.001,
    credit_rate: 0.001,
    zm_bonus_rate: 0.05,
    zm_petral_allowance_rate: 0.005,
    zm_leasing_facility_rate: 0.01,
    zm_phone_bill_rate: 0.001,
    rsm_bonus_rate: 0.05,
    rsm_petral_allowance_rate: 0.005,
    rsm_leasing_facility_rate: 0.01,
    rsm_phone_bill_rate: 0.001,
    zm_present_leader_rate: 0.02,
    rsm_present_leader_rate: 0.01,
    annual_present_leader_rate: 0.01,
    okdoi_head_rate: 0.02,
    is_active: true
  })

  useEffect(() => {
    fetchCommissionStructures()
  }, [])

  const fetchCommissionStructures = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/extended-commission-structure')
      const data = await response.json()
      
      if (data.success) {
        setStructures(data.data)
      } else {
        alert('Error fetching commission structures: ' + data.error)
      }
    } catch (error) {
      console.error('Error fetching commission structures:', error)
      alert('Failed to fetch commission structures')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (id?: string) => {
    try {
      const url = id 
        ? `/api/admin/extended-commission-structure/${id}` 
        : '/api/admin/extended-commission-structure'
      
      const method = id ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()
      
      if (data.success) {
        await fetchCommissionStructures()
        setEditingId(null)
        setShowAddForm(false)
        resetForm()
        alert(id ? 'Commission structure updated successfully!' : 'Commission structure created successfully!')
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving commission structure:', error)
      alert('Failed to save commission structure')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this commission structure?')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/extended-commission-structure/${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      
      if (data.success) {
        await fetchCommissionStructures()
        alert('Commission structure deleted successfully!')
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting commission structure:', error)
      alert('Failed to delete commission structure')
    }
  }

  const startEdit = (structure: ExtendedCommissionStructure) => {
    setFormData(structure)
    setEditingId(structure.id)
    setShowAddForm(false)
  }

  const resetForm = () => {
    setFormData({
      commission_type: 'subscription',
      package_value: 0,
      direct_commission_rate: 0.10,
      level_commission_rate: 0.02,
      is_active: true
    })
  }

  const cancelEdit = () => {
    setEditingId(null)
    setShowAddForm(false)
    resetForm()
  }

  const formatPercentage = (rate: number) => {
    return (rate * 100).toFixed(2) + '%'
  }

  const formatCurrency = (amount: number) => {
    return `Rs ${amount.toLocaleString()}`
  }

  const handleInputChange = (field: keyof ExtendedCommissionStructure, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue mx-auto mb-4"></div>
            <p className="text-gray-600">Loading commission structures...</p>
          </div>
        </div>
    )
  }

  return (
    <div className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Extended Commission Structure</h1>
                <p className="text-gray-600">Manage detailed commission rates for all categories</p>
              </div>
            </div>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Structure</span>
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Structures</p>
                  <p className="text-2xl font-bold text-gray-900">{structures.length}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Structures</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {structures.filter(s => s.is_active).length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Package Types</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Set(structures.map(s => s.commission_type)).size}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                  <Crown className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Direct Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(
                      structures.reduce((sum, s) => sum + s.direct_commission_rate, 0) / structures.length || 0
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Commission Structures Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Commission Structures</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Package Value
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Direct Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Level Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ZM Bonus
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      RSM Bonus
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {structures.map((structure) => (
                    <tr key={structure.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(structure.package_value)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {structure.commission_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatPercentage(structure.direct_commission_rate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatPercentage(structure.level_commission_rate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatPercentage(structure.zm_bonus_rate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatPercentage(structure.rsm_bonus_rate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          structure.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {structure.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => startEdit(structure)}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(structure.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
  )
}
