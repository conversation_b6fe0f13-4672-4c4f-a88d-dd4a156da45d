import { supabase, TABLES } from '@/lib/supabase'
import { CommissionStructure, CommissionTransaction } from '@/types'
import { WalletService } from './wallet'

export interface CommissionDistributionResult {
  totalDistributed: number
  transactionsCreated: number
  unallocatedAmount: number
  distributionDetails: {
    level: number
    beneficiaryId: string
    amount: number
    rate: number
  }[]
}

export interface CommissionBreakdown {
  directCommission: number
  levelCommission: number
  rsmBonus: number
  zmBonus: number
  okdoiHeadCommission: number
  totalCommissions: number
}

/**
 * CommissionSystemService - Handles commission calculations and distributions
 */
export class CommissionSystemService {
  /**
   * Get commission structure for a package value
   */
  static async getCommissionStructure(packageValue: number): Promise<CommissionStructure[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .select('*')
        .lte('package_value', packageValue)
        .eq('is_active', true)
        .order('package_value', { ascending: false })

      if (error) {
        throw new Error(`Failed to get commission structure: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting commission structure:', error)
      throw error
    }
  }

  /**
   * Get all commission structures (for admin)
   */
  static async getAllCommissionStructures(): Promise<CommissionStructure[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .select('*')
        .order('package_value', { ascending: true })

      if (error) {
        throw new Error(`Failed to get all commission structures: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting all commission structures:', error)
      throw error
    }
  }

  /**
   * Create new commission structure
   */
  static async createCommissionStructure(structure: Omit<CommissionStructure, 'id' | 'created_at' | 'updated_at'>): Promise<CommissionStructure> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .insert(structure)
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to create commission structure: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error creating commission structure:', error)
      throw error
    }
  }

  /**
   * Calculate and distribute commissions for a subscription purchase
   */
  static async distributeCommissions(
    purchaserId: string,
    subscriptionId: string,
    packageAmount: number
  ): Promise<CommissionDistributionResult> {
    try {
      const { error } = await supabase.rpc('calculate_commission_distribution', {
        purchaser_id: purchaserId,
        package_id: subscriptionId,
        package_amount: packageAmount
      })

      if (error) {
        throw new Error(`Failed to distribute commissions: ${error.message}`)
      }

      // Get the created commission transactions to return details
      const { data: transactions, error: transError } = await supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')
        .eq('subscription_purchase_id', subscriptionId)
        .eq('user_id', purchaserId)

      if (transError) {
        throw new Error(`Failed to get commission transactions: ${transError.message}`)
      }

      const distributionDetails = transactions?.map(t => ({
        level: t.commission_level,
        beneficiaryId: t.beneficiary_id,
        amount: t.commission_amount,
        rate: t.commission_rate
      })) || []

      const totalDistributed = transactions?.reduce((sum, t) => sum + t.commission_amount, 0) || 0
      const unallocatedAmount = transactions?.filter(t => t.commission_type.includes('unallocated'))
        .reduce((sum, t) => sum + t.commission_amount, 0) || 0

      return {
        totalDistributed,
        transactionsCreated: transactions?.length || 0,
        unallocatedAmount,
        distributionDetails
      }
    } catch (error) {
      console.error('Error distributing commissions:', error)
      throw error
    }
  }

  /**
   * Note: Commission processing is now automatic when subscriptions are purchased.
   * The calculate_commission_distribution database function immediately credits
   * wallets and creates processed commission transaction records.
   *
   * Manual commission processing is no longer needed as commissions are
   * automatically distributed and credited to user wallets in real-time.
   */

  /**
   * Get commission summary for admin dashboard
   */
  static async getCommissionSummary(): Promise<{
    totalCommissionsPaid: number
    pendingCommissions: number
    failedCommissions: number
    totalTransactions: number
    topEarners: { userId: string, fullName: string, totalEarned: number }[]
  }> {
    try {
      const [
        totalPaidResult,
        pendingResult,
        failedResult,
        totalTransactionsResult,
        topEarnersResult
      ] = await Promise.all([
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('commission_amount')
          .eq('status', 'processed'),
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('commission_amount')
          .eq('status', 'pending'),
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('*', { count: 'exact', head: true })
          .eq('status', 'failed'),
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('*', { count: 'exact', head: true }),
        supabase
          .from(TABLES.USERS)
          .select('id, full_name, total_commission_earned')
          .gt('total_commission_earned', 0)
          .order('total_commission_earned', { ascending: false })
          .limit(10)
      ])

      const totalCommissionsPaid = totalPaidResult.data?.reduce((sum, t) => sum + t.commission_amount, 0) || 0
      const pendingCommissions = pendingResult.data?.reduce((sum, t) => sum + t.commission_amount, 0) || 0
      const failedCommissions = failedResult.count || 0
      const totalTransactions = totalTransactionsResult.count || 0
      const topEarners = topEarnersResult.data?.map(user => ({
        userId: user.id,
        fullName: user.full_name || 'Unknown',
        totalEarned: user.total_commission_earned || 0
      })) || []

      return {
        totalCommissionsPaid,
        pendingCommissions,
        failedCommissions,
        totalTransactions,
        topEarners
      }
    } catch (error) {
      console.error('Error getting commission summary:', error)
      throw error
    }
  }

  /**
   * Update commission structure rates
   */
  static async updateCommissionStructure(
    id: string,
    updates: Partial<CommissionStructure>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .update(updates)
        .eq('id', id)

      if (error) {
        throw new Error(`Failed to update commission structure: ${error.message}`)
      }
    } catch (error) {
      console.error('Error updating commission structure:', error)
      throw error
    }
  }

  /**
   * Get commission transactions for a specific user with filters
   */
  static async getUserCommissionTransactions(
    userId: string,
    filters: {
      status?: string
      commissionType?: string
      dateFrom?: string
      dateTo?: string
    } = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ transactions: CommissionTransaction[], total: number }> {
    try {
      const offset = (page - 1) * limit
      
      let query = supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')
        .eq('beneficiary_id', userId)

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      if (filters.commissionType) {
        query = query.eq('commission_type', filters.commissionType)
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }

      const [dataResult, countResult] = await Promise.all([
        query
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1),
        query.select('*', { count: 'exact', head: true })
      ])

      if (dataResult.error) {
        throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Failed to count commission transactions: ${countResult.error.message}`)
      }

      return {
        transactions: dataResult.data || [],
        total: countResult.count || 0
      }
    } catch (error) {
      console.error('Error getting user commission transactions:', error)
      throw error
    }
  }

  /**
   * Get detailed commission analytics for admin reports
   */
  static async getCommissionAnalytics(filters: {
    dateFrom?: string
    dateTo?: string
    status?: string
    commissionType?: string
    level?: string
  } = {}): Promise<{
    transactions: CommissionTransaction[]
    total: number
    monthlyData: { month: string, amount: number, count: number }[]
    levelDistribution: { level: number, amount: number, count: number }[]
    typeDistribution: { type: string, amount: number, count: number }[]
  }> {
    try {
      let query = supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      if (filters.commissionType) {
        query = query.eq('commission_type', filters.commissionType)
      }
      if (filters.level) {
        query = query.eq('commission_level', parseInt(filters.level))
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }

      const [dataResult, countResult] = await Promise.all([
        query.order('created_at', { ascending: false }).limit(1000),
        query.select('*', { count: 'exact', head: true })
      ])

      if (dataResult.error) {
        throw new Error(`Failed to get commission analytics: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Failed to count commission analytics: ${countResult.error.message}`)
      }

      const transactions = dataResult.data || []

      // Calculate monthly data
      const monthlyMap = new Map<string, { amount: number, count: number }>()
      transactions.forEach(t => {
        const month = new Date(t.created_at).toISOString().slice(0, 7) // YYYY-MM
        const existing = monthlyMap.get(month) || { amount: 0, count: 0 }
        monthlyMap.set(month, {
          amount: existing.amount + t.commission_amount,
          count: existing.count + 1
        })
      })

      const monthlyData = Array.from(monthlyMap.entries()).map(([month, data]) => ({
        month,
        ...data
      })).sort((a, b) => a.month.localeCompare(b.month))

      // Calculate level distribution
      const levelMap = new Map<number, { amount: number, count: number }>()
      transactions.forEach(t => {
        const existing = levelMap.get(t.commission_level) || { amount: 0, count: 0 }
        levelMap.set(t.commission_level, {
          amount: existing.amount + t.commission_amount,
          count: existing.count + 1
        })
      })

      const levelDistribution = Array.from(levelMap.entries()).map(([level, data]) => ({
        level,
        ...data
      })).sort((a, b) => a.level - b.level)

      // Calculate type distribution
      const typeMap = new Map<string, { amount: number, count: number }>()
      transactions.forEach(t => {
        const existing = typeMap.get(t.commission_type) || { amount: 0, count: 0 }
        typeMap.set(t.commission_type, {
          amount: existing.amount + t.commission_amount,
          count: existing.count + 1
        })
      })

      const typeDistribution = Array.from(typeMap.entries()).map(([type, data]) => ({
        type,
        ...data
      }))

      return {
        transactions,
        total: countResult.count || 0,
        monthlyData,
        levelDistribution,
        typeDistribution
      }
    } catch (error) {
      console.error('Error getting commission analytics:', error)
      throw error
    }
  }

  /**
   * Get commission transactions with pagination and filters
   */
  static async getCommissionTransactionsWithFilters(
    filters: {
      dateFrom?: string
      dateTo?: string
      status?: string
      commissionType?: string
      level?: string
    } = {},
    page: number = 1,
    limit: number = 50
  ): Promise<{ transactions: CommissionTransaction[], total: number }> {
    try {
      const offset = (page - 1) * limit

      let query = supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      if (filters.commissionType) {
        query = query.eq('commission_type', filters.commissionType)
      }
      if (filters.level) {
        query = query.eq('commission_level', parseInt(filters.level))
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }

      const [dataResult, countResult] = await Promise.all([
        query
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1),
        query.select('*', { count: 'exact', head: true })
      ])

      if (dataResult.error) {
        throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Failed to count commission transactions: ${countResult.error.message}`)
      }

      return {
        transactions: dataResult.data || [],
        total: countResult.count || 0
      }
    } catch (error) {
      console.error('Error getting commission transactions with filters:', error)
      throw error
    }
  }

  /**
   * Get commission breakdown by type for a user
   */
  static async getCommissionBreakdown(userId: string): Promise<CommissionBreakdown> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('commission_type, commission_amount')
        .eq('beneficiary_id', userId)
        .eq('status', 'processed')

      if (error) {
        throw new Error(`Failed to get commission breakdown: ${error.message}`)
      }

      const breakdown: CommissionBreakdown = {
        directCommission: 0,
        levelCommission: 0,
        rsmBonus: 0,
        zmBonus: 0,
        okdoiHeadCommission: 0,
        totalCommissions: 0
      }

      data?.forEach(transaction => {
        const amount = parseFloat(transaction.commission_amount.toString())

        switch (transaction.commission_type) {
          case 'direct_commission':
            breakdown.directCommission += amount
            break
          case 'level_commission':
            breakdown.levelCommission += amount
            break
          case 'rsm_bonus':
            breakdown.rsmBonus += amount
            break
          case 'zonal_manager_bonus':
            breakdown.zmBonus += amount
            break
          case 'okdoi_head_commission':
            breakdown.okdoiHeadCommission += amount
            break
        }
        breakdown.totalCommissions += amount
      })

      return breakdown
    } catch (error) {
      console.error('Error getting commission breakdown:', error)
      throw error
    }
  }

  /**
   * Get extended commission breakdown with all commission types
   */
  static async getExtendedCommissionBreakdown(userId: string): Promise<{
    directCommission: number
    levelCommission: number
    voucherCommission: number
    festivalBonus: number
    savingCommission: number
    giftCenterCommission: number
    entertainmentCommission: number
    medicalCommission: number
    educationCommission: number
    creditCommission: number
    zmBonuses: number
    petralAllowanceZM: number
    leasingFacilityZM: number
    phoneBillZM: number
    rsmBonuses: number
    petralAllowanceRSM: number
    leasingFacilityRSM: number
    phoneBillRSM: number
    okdoiHeadCommission: number
    totalCommissions: number
  }> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('commission_type, commission_amount')
        .eq('beneficiary_id', userId)
        .eq('status', 'processed')

      if (error) {
        throw new Error(`Failed to get extended commission breakdown: ${error.message}`)
      }

      const breakdown = {
        directCommission: 0,
        levelCommission: 0,
        voucherCommission: 0,
        festivalBonus: 0,
        savingCommission: 0,
        giftCenterCommission: 0,
        entertainmentCommission: 0,
        medicalCommission: 0,
        educationCommission: 0,
        creditCommission: 0,
        zmBonuses: 0,
        petralAllowanceZM: 0,
        leasingFacilityZM: 0,
        phoneBillZM: 0,
        rsmBonuses: 0,
        petralAllowanceRSM: 0,
        leasingFacilityRSM: 0,
        phoneBillRSM: 0,
        okdoiHeadCommission: 0,
        totalCommissions: 0
      }

      data?.forEach(transaction => {
        const amount = parseFloat(transaction.commission_amount.toString())

        switch (transaction.commission_type) {
          case 'direct_commission':
            breakdown.directCommission += amount
            break
          case 'level_commission':
            breakdown.levelCommission += amount
            break
          case 'voucher':
            breakdown.voucherCommission += amount
            break
          case 'festival_bonus':
            breakdown.festivalBonus += amount
            break
          case 'saving':
            breakdown.savingCommission += amount
            break
          case 'gift_center':
            breakdown.giftCenterCommission += amount
            break
          case 'entertainment':
            breakdown.entertainmentCommission += amount
            break
          case 'medical':
            breakdown.medicalCommission += amount
            break
          case 'education':
            breakdown.educationCommission += amount
            break
          case 'credit':
            breakdown.creditCommission += amount
            break
          case 'zm_bonus':
            breakdown.zmBonuses += amount
            break
          case 'zm_petral_allowance':
            breakdown.petralAllowanceZM += amount
            break
          case 'zm_leasing_facility':
            breakdown.leasingFacilityZM += amount
            break
          case 'zm_phone_bill':
            breakdown.phoneBillZM += amount
            break
          case 'rsm_bonus':
            breakdown.rsmBonuses += amount
            break
          case 'rsm_petral_allowance':
            breakdown.petralAllowanceRSM += amount
            break
          case 'rsm_leasing_facility':
            breakdown.leasingFacilityRSM += amount
            break
          case 'rsm_phone_bill':
            breakdown.phoneBillRSM += amount
            break
          // Handle OKDOI Head specific commission types
          case 'okdoi_head_commission':
          case 'okdoi_head':
            breakdown.okdoiHeadCommission += amount
            break
          default:
            // Log unknown commission types for debugging
            console.warn(`Unknown commission type: ${transaction.commission_type}`)
            break
        }
        breakdown.totalCommissions += amount
      })

      return breakdown
    } catch (error) {
      console.error('Error getting extended commission breakdown:', error)
      throw error
    }
  }
}
