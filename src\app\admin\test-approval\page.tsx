'use client'

import React, { useState } from 'react'
import { AdminService } from '@/lib/services/admin'

export default function TestApprovalPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<string>('')
  const [error, setError] = useState<string>('')

  const testShopApproval = async () => {
    setLoading(true)
    setResult('')
    setError('')
    
    try {
      console.log('🧪 Starting shop approval test...')
      setResult(prev => prev + '🧪 Starting shop approval test...\n')
      
      // 1. Check admin status
      console.log('1. Checking admin status...')
      setResult(prev => prev + '1. Checking admin status...\n')
      
      const isAdmin = await AdminService.isAdmin()
      console.log('Admin status:', isAdmin)
      setResult(prev => prev + `   Admin status: ${isAdmin}\n`)
      
      if (!isAdmin) {
        throw new Error('User is not admin')
      }
      
      // 2. Get pending shops
      console.log('2. Getting pending shops...')
      setResult(prev => prev + '2. Getting pending shops...\n')
      
      const { shops, total } = await AdminService.getAllShops('pending', 1, 5)
      console.log(`Found ${total} pending shops`)
      setResult(prev => prev + `   Found ${total} pending shops\n`)
      
      if (shops.length === 0) {
        setResult(prev => prev + '   No pending shops to test with\n')
        return
      }
      
      const testShop = shops[0]
      console.log(`Testing with shop: ${testShop.name} (${testShop.id})`)
      setResult(prev => prev + `   Testing with shop: ${testShop.name} (${testShop.id})\n`)
      
      // 3. Test shop approval
      console.log('3. Approving shop...')
      setResult(prev => prev + '3. Approving shop...\n')
      
      await AdminService.updateShopStatus(testShop.id, 'approved')
      console.log('Shop approved successfully!')
      setResult(prev => prev + '   ✅ Shop approved successfully!\n')
      
      // 4. Verify approval
      console.log('4. Verifying approval...')
      setResult(prev => prev + '4. Verifying approval...\n')
      
      const { shops: approvedShops } = await AdminService.getAllShops('approved', 1, 10)
      const approvedShop = approvedShops.find(s => s.id === testShop.id)
      
      if (approvedShop) {
        console.log('✅ Shop approval verified!')
        setResult(prev => prev + '   ✅ Shop approval verified!\n')
        setResult(prev => prev + '\n🎉 All tests passed!\n')
      } else {
        throw new Error('Shop approval verification failed')
      }
      
    } catch (err) {
      console.error('❌ Test failed:', err)
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(`❌ Test failed: ${errorMessage}`)
      setResult(prev => prev + `\n❌ Test failed: ${errorMessage}\n`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Shop Approval Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <p className="text-gray-600 mb-4">
            This page tests the shop approval functionality to help debug any issues.
          </p>
          
          <button
            onClick={testShopApproval}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Shop Approval'}
          </button>
          
          {(result || error) && (
            <div className="mt-6">
              <h3 className="font-semibold mb-2">Test Results:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap">
                {result}
                {error && <span className="text-red-600">{error}</span>}
              </pre>
            </div>
          )}
        </div>
      </div>
  )
}
