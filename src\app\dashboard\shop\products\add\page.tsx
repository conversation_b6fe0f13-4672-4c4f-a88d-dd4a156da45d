'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Package,
  ArrowLeft,
  Save,
  Upload,
  Plus,
  X,
  AlertCircle,
  Trash2
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { VendorShopService } from '@/lib/services/vendorShops'
import { ShopCategoryService } from '@/lib/services/shopCategories'
import { ShopProductService } from '@/lib/services/shopProducts'
import { StorageService } from '@/lib/services/storage'
import { VendorShop, ShopCategory, ShopSubcategory } from '@/types'
import { showAlert } from '@/components/ui/ConfirmationDialog'

interface ProductFormData {
  // Ad data
  title: string
  description: string
  price: string
  condition: 'new' | 'used' | 'refurbished'
  negotiable: boolean
  contact_phone: string
  
  // Product-specific data
  category_id: string
  subcategory_id: string
  sku: string
  stock_quantity: string
  min_order_quantity: string
  weight: string
  dimensions: {
    length: string
    width: string
    height: string
  }
  is_digital: boolean
  download_url: string
  
  // Variants
  variants: Array<{
    name: string
    options: string[]
  }>
  
  // Images
  images: File[]
}

export default function AddProductPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [userShop, setUserShop] = useState<VendorShop | null>(null)
  const [categories, setCategories] = useState<ShopCategory[]>([])
  const [subcategories, setSubcategories] = useState<ShopSubcategory[]>([])
  const [error, setError] = useState<string | null>(null)

  const [formData, setFormData] = useState<ProductFormData>({
    title: '',
    description: '',
    price: '',
    condition: 'new',
    negotiable: false,
    contact_phone: '',
    category_id: '',
    subcategory_id: '',
    sku: '',
    stock_quantity: '1',
    min_order_quantity: '1',
    weight: '',
    dimensions: {
      length: '',
      width: '',
      height: ''
    },
    is_digital: false,
    download_url: '',
    variants: [],
    images: []
  })

  useEffect(() => {
    if (user) {
      loadInitialData()
    }
  }, [user])

  useEffect(() => {
    if (formData.category_id) {
      loadSubcategories(formData.category_id)
    } else {
      setSubcategories([])
      setFormData(prev => ({ ...prev, subcategory_id: '' }))
    }
  }, [formData.category_id])

  const loadInitialData = async () => {
    try {
      setLoading(true)
      
      // Load user's shop
      const shops = await VendorShopService.getUserShops(user!.id)
      const approvedShop = shops.find(shop => shop.status === 'approved')
      
      if (!approvedShop) {
        setError('You need an approved shop to add products. Please create a shop first.')
        return
      }
      
      setUserShop(approvedShop)
      
      // Load categories
      const categoriesData = await ShopCategoryService.getAllCategories()
      setCategories(categoriesData)
      
      // Set default contact phone
      if (approvedShop.contact_phone) {
        setFormData(prev => ({ ...prev, contact_phone: approvedShop.contact_phone || '' }))
      }
      
    } catch (error) {
      console.error('Error loading initial data:', error)
      setError('Failed to load required data. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const loadSubcategories = async (categoryId: string) => {
    try {
      const subcategoriesData = await ShopCategoryService.getSubcategories(categoryId)
      setSubcategories(subcategoriesData)
    } catch (error) {
      console.error('Error loading subcategories:', error)
    }
  }

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleDimensionChange = (dimension: keyof ProductFormData['dimensions'], value: string) => {
    setFormData(prev => ({
      ...prev,
      dimensions: { ...prev.dimensions, [dimension]: value }
    }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length + formData.images.length > 10) {
      showAlert({
        title: 'Too Many Images',
        message: 'You can upload a maximum of 10 images per product.',
        variant: 'warning'
      })
      return
    }
    setFormData(prev => ({ ...prev, images: [...prev.images, ...files] }))
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const addVariant = () => {
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, { name: '', options: [''] }]
    }))
  }

  const removeVariant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter((_, i) => i !== index)
    }))
  }

  const updateVariant = (index: number, field: 'name' | 'options', value: any) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants.map((variant, i) => 
        i === index ? { ...variant, [field]: value } : variant
      )
    }))
  }

  const addVariantOption = (variantIndex: number) => {
    const newVariants = [...formData.variants]
    newVariants[variantIndex].options.push('')
    setFormData(prev => ({ ...prev, variants: newVariants }))
  }

  const removeVariantOption = (variantIndex: number, optionIndex: number) => {
    const newVariants = [...formData.variants]
    newVariants[variantIndex].options = newVariants[variantIndex].options.filter((_, i) => i !== optionIndex)
    setFormData(prev => ({ ...prev, variants: newVariants }))
  }

  const updateVariantOption = (variantIndex: number, optionIndex: number, value: string) => {
    const newVariants = [...formData.variants]
    newVariants[variantIndex].options[optionIndex] = value
    setFormData(prev => ({ ...prev, variants: newVariants }))
  }

  const validateForm = (): string | null => {
    if (!formData.title.trim()) return 'Product title is required'
    if (!formData.description.trim()) return 'Product description is required'
    if (!formData.price || parseFloat(formData.price) <= 0) return 'Valid price is required'
    if (!formData.category_id) return 'Category is required'
    if (!formData.subcategory_id) return 'Subcategory is required'
    if (!formData.stock_quantity || parseInt(formData.stock_quantity) < 0) return 'Valid stock quantity is required'
    if (formData.images.length === 0) return 'At least one product image is required'
    if (formData.is_digital && !formData.download_url.trim()) return 'Download URL is required for digital products'
    
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user || !userShop) return
    
    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }
    
    try {
      setLoading(true)
      setError(null)

      // Upload images first
      const imageUrls: string[] = []
      for (const image of formData.images) {
        const imageUrl = await StorageService.uploadImage(image, user.id)
        imageUrls.push(imageUrl)
      }

      // Create the product directly (no ad creation)
      const productData = {
        shop_id: userShop.id,
        category_id: formData.category_id || undefined,
        subcategory_id: formData.subcategory_id || undefined,
        title: formData.title,
        description: formData.description,
        price: parseFloat(formData.price),
        currency: 'LKR',
        condition: formData.condition,
        negotiable: formData.negotiable,
        contact_phone: formData.contact_phone,
        sku: formData.sku || undefined,
        stock_quantity: parseInt(formData.stock_quantity),
        min_order_quantity: parseInt(formData.min_order_quantity) || 1,
        weight: formData.weight ? parseFloat(formData.weight) : undefined,
        dimensions: (formData.dimensions.length || formData.dimensions.width || formData.dimensions.height) ? {
          length: parseFloat(formData.dimensions.length) || 0,
          width: parseFloat(formData.dimensions.width) || 0,
          height: parseFloat(formData.dimensions.height) || 0
        } : undefined,
        is_digital: formData.is_digital,
        download_url: formData.download_url || undefined,
        variants: formData.variants.filter(v => v.name && v.options.some(o => o.trim())),
        status: 'active' as const
      }

      await ShopProductService.createProduct(productData, imageUrls)

      await showAlert({
        title: 'Success',
        message: 'Product added successfully!',
        variant: 'success'
      })

      router.push('/dashboard/shop')

    } catch (error) {
      console.error('Error creating product:', error)
      setError(error instanceof Error ? error.message : 'Failed to create product')
    } finally {
      setLoading(false)
    }
  }

  if (loading && !userShop) {
    return (
      <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
        </div>
    )
  }

  if (error && !userShop) {
    return (
      <div className="max-w-2xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-900 mb-2">Cannot Add Product</h3>
            <p className="text-red-700 mb-4">{error}</p>
            <button
              onClick={() => router.push('/create-shop')}
              className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Create Shop
            </button>
          </div>
        </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="text-gray-400 hover:text-gray-600"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Add New Product</h1>
              <p className="text-gray-600">Add a product to {userShop?.name}</p>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
            <span className="text-red-700">{error}</span>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Product Title *
                </label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Enter product title"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  rows={4}
                  required
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Describe your product in detail"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price (Rs) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Condition *
                </label>
                <select
                  value={formData.condition}
                  onChange={(e) => handleInputChange('condition', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                >
                  <option value="new">New</option>
                  <option value="used">Used</option>
                  <option value="refurbished">Refurbished</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  value={formData.category_id}
                  onChange={(e) => handleInputChange('category_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subcategory *
                </label>
                <select
                  value={formData.subcategory_id}
                  onChange={(e) => handleInputChange('subcategory_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  required
                  disabled={!formData.category_id}
                >
                  <option value="">Select a subcategory</option>
                  {subcategories.map((subcategory) => (
                    <option key={subcategory.id} value={subcategory.id}>
                      {subcategory.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Phone
                </label>
                <input
                  type="tel"
                  value={formData.contact_phone}
                  onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Enter contact phone"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="negotiable"
                  checked={formData.negotiable}
                  onChange={(e) => handleInputChange('negotiable', e.target.checked)}
                  className="h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded"
                />
                <label htmlFor="negotiable" className="ml-2 text-sm text-gray-700">
                  Price is negotiable
                </label>
              </div>
            </div>
          </div>

          {/* Product Details */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SKU (Optional)
                </label>
                <input
                  type="text"
                  value={formData.sku}
                  onChange={(e) => handleInputChange('sku', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Product SKU"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Stock Quantity *
                </label>
                <input
                  type="number"
                  min="0"
                  required
                  value={formData.stock_quantity}
                  onChange={(e) => handleInputChange('stock_quantity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Available quantity"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Order Quantity
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.min_order_quantity}
                  onChange={(e) => handleInputChange('min_order_quantity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weight (kg)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.weight}
                  onChange={(e) => handleInputChange('weight', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  placeholder="Product weight"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dimensions (cm)
                </label>
                <div className="grid grid-cols-3 gap-3">
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.dimensions.length}
                    onChange={(e) => handleDimensionChange('length', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    placeholder="Length"
                  />
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.dimensions.width}
                    onChange={(e) => handleDimensionChange('width', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    placeholder="Width"
                  />
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.dimensions.height}
                    onChange={(e) => handleDimensionChange('height', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    placeholder="Height"
                  />
                </div>
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="is_digital"
                    checked={formData.is_digital}
                    onChange={(e) => handleInputChange('is_digital', e.target.checked)}
                    className="h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded"
                  />
                  <label htmlFor="is_digital" className="ml-2 text-sm text-gray-700">
                    This is a digital product
                  </label>
                </div>

                {formData.is_digital && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Download URL *
                    </label>
                    <input
                      type="url"
                      required={formData.is_digital}
                      value={formData.download_url}
                      onChange={(e) => handleInputChange('download_url', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      placeholder="https://example.com/download-link"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Product Images */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Images *</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload Images (Max 10)
                </label>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Upload high-quality images of your product. First image will be the main image.
                </p>
              </div>

              {formData.images.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(image)}
                        alt={`Product ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg border border-gray-200"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                      {index === 0 && (
                        <span className="absolute bottom-2 left-2 bg-primary-blue text-white text-xs px-2 py-1 rounded">
                          Main
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Product Variants */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Product Variants (Optional)</h3>
              <button
                type="button"
                onClick={addVariant}
                className="flex items-center px-3 py-2 text-sm bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Variant
              </button>
            </div>

            {formData.variants.length === 0 ? (
              <p className="text-gray-500 text-sm">
                Add variants like size, color, or style if your product has different options.
              </p>
            ) : (
              <div className="space-y-4">
                {formData.variants.map((variant, variantIndex) => (
                  <div key={variantIndex} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <input
                        type="text"
                        value={variant.name}
                        onChange={(e) => updateVariant(variantIndex, 'name', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent mr-3"
                        placeholder="Variant name (e.g., Size, Color)"
                      />
                      <button
                        type="button"
                        onClick={() => removeVariant(variantIndex)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">Options:</label>
                      {variant.options.map((option, optionIndex) => (
                        <div key={optionIndex} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={option}
                            onChange={(e) => updateVariantOption(variantIndex, optionIndex, e.target.value)}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                            placeholder="Option value (e.g., Small, Red)"
                          />
                          <button
                            type="button"
                            onClick={() => removeVariantOption(variantIndex, optionIndex)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => addVariantOption(variantIndex)}
                        className="text-primary-blue hover:text-primary-blue/80 text-sm"
                      >
                        + Add Option
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Adding Product...' : 'Add Product'}
            </button>
          </div>
        </form>
      </div>
  )
}
